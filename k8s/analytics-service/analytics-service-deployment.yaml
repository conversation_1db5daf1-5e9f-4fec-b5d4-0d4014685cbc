apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-service
  namespace: timetable-system
  labels:
    app: analytics-service
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: analytics-service
  template:
    metadata:
      labels:
        app: analytics-service
        component: microservice
    spec:
      containers:
      - name: analytics-service
        image: hust001/dagito-analytics-service:latest
        ports:
        - containerPort: 8006
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: ANALYTICS_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        - name: DEBUG
          value: "False"
        - name: ALLOWED_HOSTS
          value: "localhost,127.0.0.1,analytics-service,api-gateway"
        - name: USER_SERVICE_URL
          value: "http://user-service:8002"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        - name: PREFERENCE_SERVICE_URL
          value: "http://preference-service:8003"
        - name: TIMETABLE_SERVICE_URL
          value: "http://timetable-service:8004"
        - name: NOTIFICATION_SERVICE_URL
          value: "http://notification-service:8005"
        envFrom:
        - configMapRef:
            name: analytics-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/health/
            port: 8006
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health/
            port: 8006
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
        - name: media-files
          mountPath: /app/media
        - name: logs
          mountPath: /app/logs
        - name: reports
          mountPath: /app/media/reports
      initContainers:
      - name: migrate
        image: hust001/dagito-analytics-service:latest
        command: ['python', 'manage.py', 'migrate']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: ANALYTICS_SECRET_KEY
        envFrom:
        - configMapRef:
            name: analytics-config
      - name: collectstatic
        image: hust001/dagito-analytics-service:latest
        command: ['python', 'manage.py', 'collectstatic', '--noinput']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: ANALYTICS_SECRET_KEY
        envFrom:
        - configMapRef:
            name: analytics-config
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
      volumes:
      - name: static-files
        persistentVolumeClaim:
          claimName: analytics-static-pvc
      - name: media-files
        persistentVolumeClaim:
          claimName: analytics-media-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: analytics-logs-pvc
      - name: reports
        persistentVolumeClaim:
          claimName: analytics-reports-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: analytics-service
  namespace: timetable-system
  labels:
    app: analytics-service
    component: microservice
spec:
  ports:
  - port: 8006
    targetPort: 8006
    protocol: TCP
  selector:
    app: analytics-service
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: analytics-static-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: analytics-media-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: analytics-logs-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: analytics-reports-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: analytics-config
  namespace: timetable-system
data:
  ANALYTICS_DATA_RETENTION_DAYS: "365"
  ANALYTICS_CACHE_TIMEOUT: "3600"
  REPORT_GENERATION_TIMEOUT: "300"
  PANDAS_CHUNK_SIZE: "10000"
  MAX_CONCURRENT_REPORTS: "5"
  MATPLOTLIB_BACKEND: "Agg"
  CHART_DEFAULT_WIDTH: "12"
  CHART_DEFAULT_HEIGHT: "8"
  CHART_DPI: "300"
  REPORT_RETENTION_DAYS: "30"
  DASHBOARD_REFRESH_INTERVAL: "300"
  DASHBOARD_CACHE_TIMEOUT: "600"
  CELERY_WORKER_CONCURRENCY: "4"
  CELERY_WORKER_MAX_TASKS_PER_CHILD: "1000"
