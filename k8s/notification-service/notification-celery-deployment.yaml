apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-celery-worker
  namespace: timetable-system
  labels:
    app: notification-celery-worker
    component: worker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: notification-celery-worker
  template:
    metadata:
      labels:
        app: notification-celery-worker
        component: worker
    spec:
      containers:
      - name: celery-worker
        image: hust001/dagito-notification-service:latest
        command: ["celery", "-A", "notification_service", "worker", "--loglevel=info", "--concurrency=2", "--queues=email,sms,push,websocket,default"]
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: NOTIFICATION_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        - name: EMAIL_HOST_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: EMAIL_HOST_PASSWORD
        - name: SENDGRID_API_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: SENDGRID_API_KEY
        - name: TWILIO_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: TWILIO_AUTH_TOKEN
        - name: FCM_SERVER_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: FCM_SERVER_KEY
        - name: USER_SERVICE_URL
          value: "http://user-service:8002"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        - name: PREFERENCE_SERVICE_URL
          value: "http://preference-service:8003"
        - name: TIMETABLE_SERVICE_URL
          value: "http://timetable-service:8004"
        envFrom:
        - configMapRef:
            name: notification-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: media-files
          mountPath: /app/media
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: media-files
        persistentVolumeClaim:
          claimName: notification-media-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: notification-logs-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-celery-beat
  namespace: timetable-system
  labels:
    app: notification-celery-beat
    component: scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: notification-celery-beat
  template:
    metadata:
      labels:
        app: notification-celery-beat
        component: scheduler
    spec:
      containers:
      - name: celery-beat
        image: hust001/dagito-notification-service:latest
        command: ["celery", "-A", "notification_service", "beat", "--loglevel=info", "--scheduler=django_celery_beat.schedulers:DatabaseScheduler"]
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: NOTIFICATION_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        envFrom:
        - configMapRef:
            name: notification-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: notification-logs-pvc
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: notification-config
  namespace: timetable-system
data:
  EMAIL_BACKEND: "django.core.mail.backends.smtp.EmailBackend"
  EMAIL_HOST: "smtp.gmail.com"
  EMAIL_PORT: "587"
  EMAIL_USE_TLS: "True"
  EMAIL_HOST_USER: "<EMAIL>"
  DEFAULT_FROM_EMAIL: "<EMAIL>"
  USE_SENDGRID: "False"
  USE_SMS: "False"
  USE_PUSH_NOTIFICATIONS: "False"
  NOTIFICATION_RETENTION_DAYS: "90"
  MAX_NOTIFICATIONS_PER_USER: "1000"
  EMAIL_RATE_LIMIT_PER_HOUR: "100"
  SMS_RATE_LIMIT_PER_HOUR: "20"
  WEBSOCKET_HEARTBEAT_INTERVAL: "30"
  WEBSOCKET_MAX_CONNECTIONS_PER_USER: "5"
  CELERY_WORKER_CONCURRENCY: "2"
  CELERY_WORKER_MAX_TASKS_PER_CHILD: "1000"
