apiVersion: apps/v1
kind: Deployment
metadata:
  name: timetable-service
  namespace: timetable-system
  labels:
    app: timetable-service
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: timetable-service
  template:
    metadata:
      labels:
        app: timetable-service
        component: microservice
    spec:
      containers:
      - name: timetable-service
        image: hust001/dagito-timetable-service:latest
        ports:
        - containerPort: 8004
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: TIMETABLE_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        - name: DEBUG
          value: "False"
        - name: ALLOWED_HOSTS
          value: "localhost,127.0.0.1,timetable-service,api-gateway"
        - name: USER_SERVICE_URL
          value: "http://user-service:8002"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        - name: PREFERENCE_SERVICE_URL
          value: "http://preference-service:8003"
        - name: NOTIFICATION_SERVICE_URL
          value: "http://notification-service:8005"
        - name: GENETIC_ALGORITHM_POPULATION_SIZE
          value: "100"
        - name: GENETIC_ALGORITHM_GENERATIONS
          value: "50"
        - name: GENETIC_ALGORITHM_MUTATION_RATE
          value: "0.1"
        - name: GENETIC_ALGORITHM_CROSSOVER_RATE
          value: "0.8"
        - name: TIMETABLE_GENERATION_TIMEOUT
          value: "300"
        - name: EXPORT_FILE_RETENTION_DAYS
          value: "7"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/health/
            port: 8004
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health/
            port: 8004
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
        - name: media-files
          mountPath: /app/media
        - name: logs
          mountPath: /app/logs
      initContainers:
      - name: migrate
        image: hust001/dagito-timetable-service:latest
        command: ['python', 'manage.py', 'migrate']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: TIMETABLE_SECRET_KEY
        envFrom:
        - configMapRef:
            name: timetable-config
      - name: collectstatic
        image: hust001/dagito-timetable-service:latest
        command: ['python', 'manage.py', 'collectstatic', '--noinput']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: TIMETABLE_SECRET_KEY
        envFrom:
        - configMapRef:
            name: timetable-config
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
      volumes:
      - name: static-files
        persistentVolumeClaim:
          claimName: timetable-static-pvc
      - name: media-files
        persistentVolumeClaim:
          claimName: timetable-media-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: timetable-logs-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: timetable-service
  namespace: timetable-system
  labels:
    app: timetable-service
    component: microservice
spec:
  ports:
  - port: 8004
    targetPort: 8004
    protocol: TCP
  selector:
    app: timetable-service
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: timetable-static-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: timetable-media-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: timetable-logs-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 2Gi
