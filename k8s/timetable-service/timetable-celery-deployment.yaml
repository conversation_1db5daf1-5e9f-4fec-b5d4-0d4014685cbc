apiVersion: apps/v1
kind: Deployment
metadata:
  name: timetable-celery-worker
  namespace: timetable-system
  labels:
    app: timetable-celery-worker
    component: worker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: timetable-celery-worker
  template:
    metadata:
      labels:
        app: timetable-celery-worker
        component: worker
    spec:
      containers:
      - name: celery-worker
        image: hust001/dagito-timetable-service:latest
        command: ["celery", "-A", "timetable_service", "worker", "--loglevel=info", "--concurrency=2"]
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: TIMETABLE_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        - name: USER_SERVICE_URL
          value: "http://user-service:8002"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        - name: PREFERENCE_SERVICE_URL
          value: "http://preference-service:8003"
        - name: NOTIFICATION_SERVICE_URL
          value: "http://notification-service:8005"
        - name: GENETIC_ALGORITHM_POPULATION_SIZE
          value: "100"
        - name: GENETIC_ALGORITHM_GENERATIONS
          value: "50"
        - name: GENETIC_ALGORITHM_MUTATION_RATE
          value: "0.1"
        - name: GENETIC_ALGORITHM_CROSSOVER_RATE
          value: "0.8"
        - name: TIMETABLE_GENERATION_TIMEOUT
          value: "300"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: media-files
          mountPath: /app/media
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: media-files
        persistentVolumeClaim:
          claimName: timetable-media-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: timetable-logs-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: timetable-celery-beat
  namespace: timetable-system
  labels:
    app: timetable-celery-beat
    component: scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: timetable-celery-beat
  template:
    metadata:
      labels:
        app: timetable-celery-beat
        component: scheduler
    spec:
      containers:
      - name: celery-beat
        image: hust001/dagito-timetable-service:latest
        command: ["celery", "-A", "timetable_service", "beat", "--loglevel=info", "--scheduler=django_celery_beat.schedulers:DatabaseScheduler"]
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: TIMETABLE_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: timetable-logs-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: timetable-logs-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 2Gi
