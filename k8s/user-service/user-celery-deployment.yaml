apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-celery-worker
  namespace: timetable-system
  labels:
    app: user-celery-worker
    component: worker
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-celery-worker
  template:
    metadata:
      labels:
        app: user-celery-worker
        component: worker
    spec:
      containers:
      - name: user-celery-worker
        image: hust001/dagito-user-service:latest
        command: ['celery', '-A', 'user_service', 'worker', '--loglevel=info']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: USER_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: AUTH_SERVICE_INTERNAL_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: AUTH_SERVICE_INTERNAL_TOKEN
        - name: DEBUG
          value: "False"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-celery-beat
  namespace: timetable-system
  labels:
    app: user-celery-beat
    component: scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: user-celery-beat
  template:
    metadata:
      labels:
        app: user-celery-beat
        component: scheduler
    spec:
      containers:
      - name: user-celery-beat
        image: hust001/dagito-user-service:latest
        command: ['celery', '-A', 'user_service', 'beat', '--loglevel=info']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: USER_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: AUTH_SERVICE_INTERNAL_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: AUTH_SERVICE_INTERNAL_TOKEN
        - name: DEBUG
          value: "False"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "100m"
