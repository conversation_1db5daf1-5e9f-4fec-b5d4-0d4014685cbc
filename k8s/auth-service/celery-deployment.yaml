apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-worker
  namespace: timetable-system
  labels:
    app: celery-worker
    component: worker
spec:
  replicas: 2
  selector:
    matchLabels:
      app: celery-worker
  template:
    metadata:
      labels:
        app: celery-worker
        component: worker
    spec:
      containers:
      - name: celery-worker
        image: hust001/dagito-auth-service:latest
        command: ['celery', '-A', 'auth_service', 'worker', '--loglevel=info']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: AUTH_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: EMAIL_HOST_USER
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: EMAIL_HOST_USER
        - name: EMAIL_HOST_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: EMAIL_HOST_PASSWORD
        - name: DEBUG
          value: "False"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-beat
  namespace: timetable-system
  labels:
    app: celery-beat
    component: scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: celery-beat
  template:
    metadata:
      labels:
        app: celery-beat
        component: scheduler
    spec:
      containers:
      - name: celery-beat
        image: hust001/dagito-auth-service:latest
        command: ['celery', '-A', 'auth_service', 'beat', '--loglevel=info']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: AUTH_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: DEBUG
          value: "False"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "100m"
