apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: timetable-system
  labels:
    app: auth-service
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
        component: microservice
    spec:
      containers:
      - name: auth-service
        image: hust001/dagito-auth-service:latest
        ports:
        - containerPort: 8001
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: AUTH_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: EMAIL_HOST_USER
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: EMAIL_HOST_USER
        - name: EMAIL_HOST_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: EMAIL_HOST_PASSWORD
        - name: DEBUG
          value: "False"
        - name: ALLOWED_HOSTS
          value: "localhost,127.0.0.1,auth-service,api-gateway"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health/
            port: 8001
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health/
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
        - name: media-files
          mountPath: /app/media
      initContainers:
      - name: migrate
        image: hust001/dagito-auth-service:latest
        command: ['python', 'manage.py', 'migrate']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: AUTH_SECRET_KEY
        envFrom:
        - configMapRef:
            name: timetable-config
      - name: collectstatic
        image: hust001/dagito-auth-service:latest
        command: ['python', 'manage.py', 'collectstatic', '--noinput']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: AUTH_SECRET_KEY
        envFrom:
        - configMapRef:
            name: timetable-config
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
      volumes:
      - name: static-files
        persistentVolumeClaim:
          claimName: auth-static-pvc
      - name: media-files
        persistentVolumeClaim:
          claimName: auth-media-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: timetable-system
  labels:
    app: auth-service
    component: microservice
spec:
  ports:
  - port: 8001
    targetPort: 8001
    protocol: TCP
  selector:
    app: auth-service
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: auth-static-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: auth-media-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 2Gi
