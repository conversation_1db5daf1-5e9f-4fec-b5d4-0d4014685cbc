#!/bin/bash

# Quick Test Script for Individual Docker Images
# Tests a single service with proper dependencies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

DOCKER_USERNAME="hust001"
TEST_NETWORK="quick-test-network"

# Clean up function
cleanup() {
    print_status "Cleaning up..."
    docker stop test-mongodb test-redis test-service 2>/dev/null || true
    docker rm test-mongodb test-redis test-service 2>/dev/null || true
    docker network rm $TEST_NETWORK 2>/dev/null || true
    print_success "Cleanup completed"
}

# Test a single service
test_single_service() {
    local service_name=$1
    local image_name=$2
    local port=$3
    
    print_status "Quick test for $service_name"
    echo "=================================="
    
    # Create test network
    docker network create $TEST_NETWORK 2>/dev/null || true
    
    # Start MongoDB
    print_status "Starting MongoDB..."
    docker run -d \
        --name test-mongodb \
        --network $TEST_NETWORK \
        -e MONGO_INITDB_ROOT_USERNAME=admin \
        -e MONGO_INITDB_ROOT_PASSWORD=password123 \
        mongo:6.0 >/dev/null
    
    # Start Redis
    print_status "Starting Redis..."
    docker run -d \
        --name test-redis \
        --network $TEST_NETWORK \
        redis:7-alpine >/dev/null
    
    # Wait for infrastructure
    print_status "Waiting for infrastructure..."
    sleep 10
    
    # Pull and start the service
    print_status "Pulling and starting $service_name..."
    docker pull "$DOCKER_USERNAME/$image_name"
    
    local container_id=$(docker run -d \
        --name test-service \
        --network $TEST_NETWORK \
        -p "$port:$port" \
        -e DEBUG=True \
        -e MONGODB_URI=******************************************************************************** \
        -e REDIS_URL=redis://test-redis:6379/0 \
        -e SECRET_KEY=test-secret-key \
        -e JWT_SECRET_KEY=test-jwt-secret \
        -e ALLOWED_HOSTS=localhost,127.0.0.1 \
        "$DOCKER_USERNAME/$image_name")
    
    print_success "Container started: $container_id"
    
    # Wait for service to start
    print_status "Waiting for service to initialize..."
    sleep 30
    
    # Check if container is running
    if docker ps --filter "id=$container_id" --format "{{.ID}}" | grep -q "$container_id"; then
        print_success "✓ Container is running"
        
        # Test connectivity
        if nc -z localhost $port 2>/dev/null; then
            print_success "✓ Port $port is accessible"
        else
            print_warning "⚠ Port $port is not accessible"
        fi
        
        # Show logs
        print_status "Recent logs:"
        docker logs --tail=10 "$container_id"
        
        # Show resource usage
        print_status "Resource usage:"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" "$container_id"
        
        print_success "$service_name is working correctly!"
        
    else
        print_error "Container stopped unexpectedly"
        print_status "Container logs:"
        docker logs "$container_id"
        return 1
    fi
}

# Main function
main() {
    local service_name=${1:-"auth-service"}
    local image_name="dagito-$service_name:latest"
    local port=${2:-"8001"}
    
    print_status "Quick testing $service_name on port $port"
    
    # Check prerequisites
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v nc &> /dev/null; then
        print_error "netcat (nc) is not installed"
        exit 1
    fi
    
    # Run test
    if test_single_service "$service_name" "$image_name" "$port"; then
        print_success "🎉 $service_name test passed!"
    else
        print_error "❌ $service_name test failed!"
        exit 1
    fi
    
    # Cleanup
    cleanup
    
    print_status "Test completed successfully!"
}

# Handle script interruption
trap cleanup EXIT INT TERM

# Run main function with arguments
main "$@"
