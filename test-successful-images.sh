#!/bin/bash

# Test Successfully Built Docker Images
# Tests the images that were successfully built and pushed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

DOCKER_USERNAME="hust001"
TEST_NETWORK="success-test-network"

# Clean up function
cleanup() {
    print_status "Cleaning up test containers and network..."
    docker stop test-mongodb test-redis test-service 2>/dev/null || true
    docker rm test-mongodb test-redis test-service 2>/dev/null || true
    docker network rm $TEST_NETWORK 2>/dev/null || true
    print_success "Cleanup completed"
}

# Setup test infrastructure
setup_infrastructure() {
    print_status "Setting up test infrastructure..."
    
    # Create test network
    docker network create $TEST_NETWORK 2>/dev/null || true
    
    # Start MongoDB
    print_status "Starting MongoDB..."
    docker run -d \
        --name test-mongodb \
        --network $TEST_NETWORK \
        -e MONGO_INITDB_ROOT_USERNAME=admin \
        -e MONGO_INITDB_ROOT_PASSWORD=password123 \
        mongo:6.0 >/dev/null
    
    # Start Redis
    print_status "Starting Redis..."
    docker run -d \
        --name test-redis \
        --network $TEST_NETWORK \
        redis:7-alpine >/dev/null
    
    # Wait for services to be ready
    print_status "Waiting for infrastructure to be ready..."
    sleep 15
    
    print_success "Infrastructure ready"
}

# Test a service
test_service() {
    local service_name=$1
    local image_name=$2
    local port=$3
    local test_duration=${4:-60}
    
    print_status "Testing $service_name..."
    echo "=================================="
    
    # Pull latest image
    print_status "Pulling $DOCKER_USERNAME/$image_name..."
    docker pull "$DOCKER_USERNAME/$image_name"
    
    # Start container
    print_status "Starting container..."
    local container_id=$(docker run -d \
        --name test-service \
        --network $TEST_NETWORK \
        -p "$port:$port" \
        -e DEBUG=True \
        -e MONGODB_URI=******************************************************************************** \
        -e REDIS_URL=redis://test-redis:6379/0 \
        -e SECRET_KEY=test-secret-key-for-testing \
        -e JWT_SECRET_KEY=test-jwt-secret-key-for-testing \
        -e ALLOWED_HOSTS=localhost,127.0.0.1,$service_name \
        -e AUTH_SERVICE_URL=http://localhost:8001 \
        -e USER_SERVICE_URL=http://localhost:8002 \
        -e PREFERENCE_SERVICE_URL=http://localhost:8003 \
        -e TIMETABLE_SERVICE_URL=http://localhost:8004 \
        -e NOTIFICATION_SERVICE_URL=http://localhost:8005 \
        -e ANALYTICS_SERVICE_URL=http://localhost:8006 \
        "$DOCKER_USERNAME/$image_name")
    
    if [ $? -ne 0 ]; then
        print_error "Failed to start container for $service_name"
        return 1
    fi
    
    print_success "Container started: $container_id"
    
    # Wait for container to initialize
    print_status "Waiting for service to initialize ($test_duration seconds)..."
    sleep $test_duration
    
    # Check if container is still running
    if ! docker ps --filter "id=$container_id" --format "{{.ID}}" | grep -q "$container_id"; then
        print_error "Container stopped unexpectedly"
        print_status "Container logs:"
        docker logs "$container_id" | tail -30
        docker rm -f "$container_id" 2>/dev/null
        return 1
    fi
    
    # Test basic connectivity
    print_status "Testing basic connectivity on port $port..."
    local connectivity_status="FAILED"
    if nc -z localhost $port 2>/dev/null; then
        connectivity_status="SUCCESS"
        print_success "Port $port is accessible"
    else
        print_warning "Port $port is not accessible"
    fi
    
    # Test HTTP response
    local http_status="N/A"
    print_status "Testing HTTP response..."
    if curl -f -s "http://localhost:$port/" >/dev/null 2>&1; then
        http_status="SUCCESS"
        print_success "HTTP response successful"
    elif curl -f -s "http://localhost:$port/api/health/" >/dev/null 2>&1; then
        http_status="SUCCESS"
        print_success "Health endpoint accessible"
    else
        http_status="FAILED"
        print_warning "HTTP response failed"
    fi
    
    # Show container stats
    print_status "Container resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" "$container_id"
    
    # Show recent logs
    print_status "Recent container logs (last 15 lines):"
    docker logs --tail=15 "$container_id"
    
    # Stop and remove container
    print_status "Stopping container..."
    docker stop "$container_id" >/dev/null
    docker rm "$container_id" >/dev/null
    
    # Determine overall status
    local overall_status="PARTIAL"
    if [ "$connectivity_status" = "SUCCESS" ] && [ "$http_status" = "SUCCESS" ]; then
        overall_status="SUCCESS"
    elif [ "$connectivity_status" = "SUCCESS" ]; then
        overall_status="PARTIAL"
    else
        overall_status="FAILED"
    fi
    
    print_success "$service_name test completed - Status: $overall_status"
    echo
    
    if [ "$overall_status" = "SUCCESS" ]; then
        return 0
    else
        return 1
    fi
}

# Test frontend (different approach)
test_frontend() {
    print_status "Testing Frontend..."
    echo "=================================="
    
    # Pull latest image
    print_status "Pulling $DOCKER_USERNAME/dagito-frontend..."
    docker pull "$DOCKER_USERNAME/dagito-frontend"
    
    # Start container
    print_status "Starting frontend container..."
    local container_id=$(docker run -d \
        --name test-service \
        --network $TEST_NETWORK \
        -p "3000:3000" \
        -e REACT_APP_API_BASE_URL=http://localhost:8000/api \
        -e REACT_APP_ENV=test \
        "$DOCKER_USERNAME/dagito-frontend")
    
    if [ $? -ne 0 ]; then
        print_error "Failed to start frontend container"
        return 1
    fi
    
    print_success "Frontend container started: $container_id"
    
    # Wait for frontend to build and start
    print_status "Waiting for frontend to initialize (60 seconds)..."
    sleep 60
    
    # Check if container is still running
    if ! docker ps --filter "id=$container_id" --format "{{.ID}}" | grep -q "$container_id"; then
        print_error "Frontend container stopped unexpectedly"
        print_status "Container logs:"
        docker logs "$container_id" | tail -30
        docker rm -f "$container_id" 2>/dev/null
        return 1
    fi
    
    # Test basic connectivity
    print_status "Testing frontend connectivity on port 3000..."
    if nc -z localhost 3000 2>/dev/null; then
        print_success "Frontend port 3000 is accessible"
    else
        print_warning "Frontend port 3000 is not accessible"
    fi
    
    # Show container stats
    print_status "Frontend container resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" "$container_id"
    
    # Show recent logs
    print_status "Recent frontend logs:"
    docker logs --tail=15 "$container_id"
    
    # Stop and remove container
    print_status "Stopping frontend container..."
    docker stop "$container_id" >/dev/null
    docker rm "$container_id" >/dev/null
    
    print_success "Frontend test completed"
    echo
    return 0
}

# Main test function
main() {
    print_status "Testing successfully built Docker images"
    print_status "Docker Hub Username: $DOCKER_USERNAME"
    echo
    
    # Check prerequisites
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        print_error "curl is not installed"
        exit 1
    fi
    
    if ! command -v nc &> /dev/null; then
        print_error "netcat (nc) is not installed"
        exit 1
    fi
    
    # Setup infrastructure
    setup_infrastructure
    
    # Test results tracking
    local passed_tests=0
    local failed_tests=0
    local total_tests=3
    
    # Test successfully built services
    echo "🧪 TESTING SUCCESSFULLY BUILT SERVICES"
    echo "======================================="
    
    if test_service "timetable-service" "dagito-timetable-service" "8004" 90; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
    
    if test_service "analytics-service" "dagito-analytics-service" "8006" 60; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
    
    echo "🎨 TESTING FRONTEND SERVICE"
    echo "============================"
    
    if test_frontend; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
    
    # Cleanup
    cleanup
    
    # Final results
    echo
    echo "🏁 TEST RESULTS SUMMARY"
    echo "=================================="
    print_status "Total tests: $total_tests"
    print_success "Passed: $passed_tests"
    if [ $failed_tests -gt 0 ]; then
        print_error "Failed: $failed_tests"
    else
        print_success "Failed: $failed_tests"
    fi
    
    if [ $failed_tests -eq 0 ]; then
        print_success "🎉 All successfully built images are working correctly!"
        print_status "These services are ready for deployment!"
    else
        print_warning "⚠️  Some tests failed. Check the logs above for details."
    fi
    
    echo
    print_status "Successfully tested services:"
    echo "  - hust001/dagito-timetable-service:latest"
    echo "  - hust001/dagito-analytics-service:latest"
    echo "  - hust001/dagito-frontend:latest"
    
    echo
    print_status "Services that need to be rebuilt:"
    echo "  - dagito-auth-service (build failed)"
    echo "  - dagito-user-service (build failed)"
    echo "  - dagito-preference-service (build failed)"
    echo "  - dagito-notification-service (build failed)"
}

# Handle script interruption
trap cleanup EXIT INT TERM

# Run main function
main "$@"
