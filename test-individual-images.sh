#!/bin/bash

# Test Individual Docker Images Script
# This script tests each DAGITO microservice image individually

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

DOCKER_USERNAME="hust001"
TEST_NETWORK="dagito-test-network"

# Create test network
create_test_network() {
    print_status "Creating test network..."
    docker network create $TEST_NETWORK 2>/dev/null || true
    print_success "Test network ready"
}

# Clean up function
cleanup() {
    print_status "Cleaning up test containers and network..."
    docker ps -a --filter "network=$TEST_NETWORK" --format "{{.ID}}" | xargs -r docker rm -f
    docker network rm $TEST_NETWORK 2>/dev/null || true
    print_success "Cleanup completed"
}

# Test a service
test_service() {
    local service_name=$1
    local image_name=$2
    local port=$3
    local health_endpoint=$4
    local test_duration=${5:-30}
    
    print_status "Testing $service_name..."
    echo "=================================="
    
    # Pull latest image
    print_status "Pulling $DOCKER_USERNAME/$image_name..."
    docker pull "$DOCKER_USERNAME/$image_name"
    
    # Start container
    print_status "Starting container..."
    local container_id=$(docker run -d \
        --name "test-$service_name" \
        --network $TEST_NETWORK \
        -p "$port:$port" \
        -e DEBUG=True \
        -e MONGODB_URI=**************************************** \
        -e REDIS_URL=redis://localhost:6379/0 \
        -e SECRET_KEY=test-secret-key \
        -e JWT_SECRET_KEY=test-jwt-secret \
        -e ALLOWED_HOSTS=localhost,127.0.0.1 \
        "$DOCKER_USERNAME/$image_name")
    
    if [ $? -ne 0 ]; then
        print_error "Failed to start container for $service_name"
        return 1
    fi
    
    print_success "Container started: $container_id"
    
    # Wait for container to initialize
    print_status "Waiting for service to initialize..."
    sleep $test_duration
    
    # Check if container is still running
    if ! docker ps --filter "id=$container_id" --format "{{.ID}}" | grep -q "$container_id"; then
        print_error "Container stopped unexpectedly"
        print_status "Container logs:"
        docker logs "$container_id" | tail -20
        docker rm -f "$container_id" 2>/dev/null
        return 1
    fi
    
    # Test health endpoint if provided
    local health_status="UNKNOWN"
    if [ -n "$health_endpoint" ]; then
        print_status "Testing health endpoint: http://localhost:$port$health_endpoint"
        if curl -f -s "http://localhost:$port$health_endpoint" >/dev/null 2>&1; then
            health_status="HEALTHY"
            print_success "Health check passed"
        else
            health_status="UNHEALTHY"
            print_warning "Health check failed (may be expected without dependencies)"
        fi
    fi
    
    # Test basic connectivity
    print_status "Testing basic connectivity on port $port..."
    if nc -z localhost $port 2>/dev/null; then
        print_success "Port $port is accessible"
    else
        print_warning "Port $port is not accessible"
    fi
    
    # Show container stats
    print_status "Container resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" "$container_id"
    
    # Show recent logs
    print_status "Recent container logs:"
    docker logs --tail=10 "$container_id"
    
    # Stop and remove container
    print_status "Stopping container..."
    docker stop "$container_id" >/dev/null
    docker rm "$container_id" >/dev/null
    
    print_success "$service_name test completed - Health: $health_status"
    echo
    return 0
}

# Test frontend (different approach)
test_frontend() {
    print_status "Testing Frontend..."
    echo "=================================="
    
    # Pull latest image
    print_status "Pulling $DOCKER_USERNAME/dagito-frontend..."
    docker pull "$DOCKER_USERNAME/dagito-frontend"
    
    # Start container
    print_status "Starting frontend container..."
    local container_id=$(docker run -d \
        --name "test-frontend" \
        --network $TEST_NETWORK \
        -p "3000:3000" \
        -e REACT_APP_API_BASE_URL=http://localhost:8000/api \
        -e REACT_APP_ENV=test \
        "$DOCKER_USERNAME/dagito-frontend")
    
    if [ $? -ne 0 ]; then
        print_error "Failed to start frontend container"
        return 1
    fi
    
    print_success "Frontend container started: $container_id"
    
    # Wait for frontend to build and start
    print_status "Waiting for frontend to initialize (this may take longer)..."
    sleep 45
    
    # Check if container is still running
    if ! docker ps --filter "id=$container_id" --format "{{.ID}}" | grep -q "$container_id"; then
        print_error "Frontend container stopped unexpectedly"
        print_status "Container logs:"
        docker logs "$container_id" | tail -20
        docker rm -f "$container_id" 2>/dev/null
        return 1
    fi
    
    # Test basic connectivity
    print_status "Testing frontend connectivity on port 3000..."
    if nc -z localhost 3000 2>/dev/null; then
        print_success "Frontend port 3000 is accessible"
    else
        print_warning "Frontend port 3000 is not accessible"
    fi
    
    # Test HTTP response
    print_status "Testing HTTP response..."
    if curl -f -s "http://localhost:3000/" >/dev/null 2>&1; then
        print_success "Frontend HTTP response successful"
    else
        print_warning "Frontend HTTP response failed"
    fi
    
    # Show container stats
    print_status "Frontend container resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" "$container_id"
    
    # Show recent logs
    print_status "Recent frontend logs:"
    docker logs --tail=15 "$container_id"
    
    # Stop and remove container
    print_status "Stopping frontend container..."
    docker stop "$container_id" >/dev/null
    docker rm "$container_id" >/dev/null
    
    print_success "Frontend test completed"
    echo
    return 0
}

# Main test function
main() {
    print_status "Starting individual Docker image tests for DAGITO microservices"
    print_status "Docker Hub Username: $DOCKER_USERNAME"
    echo
    
    # Check prerequisites
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        print_error "curl is not installed"
        exit 1
    fi
    
    if ! command -v nc &> /dev/null; then
        print_error "netcat (nc) is not installed"
        exit 1
    fi
    
    # Setup
    create_test_network
    
    # Test results tracking
    local passed_tests=0
    local failed_tests=0
    local total_tests=7
    
    # Test each service
    echo "🧪 TESTING BACKEND SERVICES"
    echo "=================================="
    
    if test_service "auth-service" "dagito-auth-service" "8001" "/api/health/" 35; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
    
    if test_service "user-service" "dagito-user-service" "8002" "/api/health/" 35; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
    
    if test_service "preference-service" "dagito-preference-service" "8003" "/api/health/" 35; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
    
    if test_service "timetable-service" "dagito-timetable-service" "8004" "/api/health/" 40; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
    
    if test_service "notification-service" "dagito-notification-service" "8005" "/api/health/" 35; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
    
    if test_service "analytics-service" "dagito-analytics-service" "8006" "/api/health/" 35; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
    
    echo "🎨 TESTING FRONTEND SERVICE"
    echo "=================================="
    
    if test_frontend; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
    
    # Cleanup
    cleanup
    
    # Final results
    echo
    echo "🏁 TEST RESULTS SUMMARY"
    echo "=================================="
    print_status "Total tests: $total_tests"
    print_success "Passed: $passed_tests"
    if [ $failed_tests -gt 0 ]; then
        print_error "Failed: $failed_tests"
    else
        print_success "Failed: $failed_tests"
    fi
    
    if [ $failed_tests -eq 0 ]; then
        print_success "🎉 All Docker images are working correctly!"
        print_status "Your microservices are ready for deployment!"
    else
        print_warning "⚠️  Some tests failed. Check the logs above for details."
        print_status "You may need to investigate the failing services."
    fi
    
    echo
    print_status "Next steps:"
    echo "  1. If all tests passed: Deploy using ./deploy-production.sh"
    echo "  2. If tests failed: Check service logs and fix issues"
    echo "  3. For production: Configure proper database and dependencies"
}

# Handle script interruption
trap cleanup EXIT INT TERM

# Run main function
main "$@"
