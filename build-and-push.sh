#!/bin/bash

# Build and Push Script for DAGITO Microservices
# Docker Hub Username: hust001

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Docker Hub username
DOCKER_USERNAME="hust001"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to build Docker image
build_image() {
    local service_dir=$1
    local image_name=$2
    local port=$3
    
    print_status "Building $image_name..."
    
    if [ ! -d "$service_dir" ]; then
        print_error "Directory $service_dir does not exist!"
        return 1
    fi
    
    if [ ! -f "$service_dir/Dockerfile" ]; then
        print_error "Dockerfile not found in $service_dir!"
        return 1
    fi
    
    # Build the image
    docker build -t "$DOCKER_USERNAME/$image_name:latest" "$service_dir"
    
    if [ $? -eq 0 ]; then
        print_success "Successfully built $DOCKER_USERNAME/$image_name:latest"
        return 0
    else
        print_error "Failed to build $image_name"
        return 1
    fi
}

# Function to test Docker image
test_image() {
    local image_name=$1
    local port=$2
    local health_endpoint=$3
    
    print_status "Testing $image_name..."
    
    # Run container in detached mode
    container_id=$(docker run -d -p "$port:$port" "$DOCKER_USERNAME/$image_name:latest")
    
    if [ $? -ne 0 ]; then
        print_error "Failed to start container for $image_name"
        return 1
    fi
    
    print_status "Container started with ID: $container_id"
    
    # Wait for container to be ready
    print_status "Waiting for container to be ready..."
    sleep 10
    
    # Check if container is still running
    if ! docker ps | grep -q "$container_id"; then
        print_error "Container $container_id is not running"
        docker logs "$container_id"
        docker rm -f "$container_id" 2>/dev/null
        return 1
    fi
    
    # Test health endpoint if provided
    if [ -n "$health_endpoint" ]; then
        print_status "Testing health endpoint: $health_endpoint"
        if curl -f "http://localhost:$port$health_endpoint" >/dev/null 2>&1; then
            print_success "Health check passed for $image_name"
        else
            print_warning "Health check failed for $image_name (this might be expected if dependencies are not available)"
        fi
    fi
    
    # Stop and remove container
    docker stop "$container_id" >/dev/null
    docker rm "$container_id" >/dev/null
    
    print_success "Test completed for $image_name"
    return 0
}

# Function to push Docker image
push_image() {
    local image_name=$1
    
    print_status "Pushing $DOCKER_USERNAME/$image_name:latest to Docker Hub..."
    
    docker push "$DOCKER_USERNAME/$image_name:latest"
    
    if [ $? -eq 0 ]; then
        print_success "Successfully pushed $DOCKER_USERNAME/$image_name:latest"
        return 0
    else
        print_error "Failed to push $image_name"
        return 1
    fi
}

# Function to process a service
process_service() {
    local service_dir=$1
    local image_name=$2
    local port=$3
    local health_endpoint=$4
    
    echo
    print_status "Processing $image_name..."
    echo "=================================="
    
    # Build
    if ! build_image "$service_dir" "$image_name" "$port"; then
        return 1
    fi
    
    # Test
    if ! test_image "$image_name" "$port" "$health_endpoint"; then
        print_warning "Test failed for $image_name, but continuing..."
    fi
    
    # Push
    if ! push_image "$image_name"; then
        return 1
    fi
    
    print_success "Completed processing $image_name"
    return 0
}

# Main execution
main() {
    print_status "Starting Docker build and push process for DAGITO microservices"
    print_status "Docker Hub Username: $DOCKER_USERNAME"
    echo
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running or not accessible"
        exit 1
    fi
    
    # Check if logged into Docker Hub
    if ! docker info | grep -q "Username"; then
        print_warning "You may not be logged into Docker Hub"
        print_status "Please run: docker login"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Define services to process
    # Format: "directory:image_name:port:health_endpoint"
    services=(
        "auth-service:dagito-auth-service:8001:/api/health/"
        "user-service:dagito-user-service:8002:/api/health/"
        "preference-service:dagito-preference-service:8003:/api/health/"
        "timetable-service:dagito-timetable-service:8004:/api/health/"
        "notification-service:dagito-notification-service:8005:/api/health/"
        "analytics-service:dagito-analytics-service:8006:/api/health/"
        "frontend:dagito-frontend:3000:"
    )
    
    failed_services=()
    
    # Process each service
    for service_info in "${services[@]}"; do
        IFS=':' read -r service_dir image_name port health_endpoint <<< "$service_info"
        
        if ! process_service "$service_dir" "$image_name" "$port" "$health_endpoint"; then
            failed_services+=("$image_name")
        fi
    done
    
    # Summary
    echo
    echo "=================================="
    print_status "Build and Push Summary"
    echo "=================================="
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        print_success "All services processed successfully!"
    else
        print_error "Failed services: ${failed_services[*]}"
        exit 1
    fi
    
    print_status "All Docker images are now available on Docker Hub under $DOCKER_USERNAME/"
    echo
    print_status "You can now deploy using:"
    echo "  - docker-compose.yml (update image names to use $DOCKER_USERNAME/)"
    echo "  - Kubernetes manifests in k8s/ directory"
}

# Run main function
main "$@"
