#!/bin/bash

# Script to update Kubernetes manifests to use DockerHub images

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

DOCKER_USERNAME="hust001"

print_status "Updating Kubernetes manifests to use DockerHub images..."

# Update auth-service
print_status "Updating auth-service manifests..."
sed -i "s|image: timetable/auth-service:latest|image: $DOCKER_USERNAME/dagito-auth-service:latest|g" k8s/auth-service/auth-service-deployment.yaml

# Update user-service
print_status "Updating user-service manifests..."
sed -i "s|image: timetable/user-service:latest|image: $DOCKER_USERNAME/dagito-user-service:latest|g" k8s/user-service/user-service-deployment.yaml

# Update preference-service
print_status "Updating preference-service manifests..."
sed -i "s|image: timetable/preference-service:latest|image: $DOCKER_USERNAME/dagito-preference-service:latest|g" k8s/preference-service/preference-service-deployment.yaml

# Update timetable-service
print_status "Updating timetable-service manifests..."
sed -i "s|image: timetable/timetable-service:latest|image: $DOCKER_USERNAME/dagito-timetable-service:latest|g" k8s/timetable-service/timetable-service-deployment.yaml

# Update notification-service
print_status "Updating notification-service manifests..."
sed -i "s|image: timetable/notification-service:latest|image: $DOCKER_USERNAME/dagito-notification-service:latest|g" k8s/notification-service/notification-service-deployment.yaml

# Update analytics-service
print_status "Updating analytics-service manifests..."
sed -i "s|image: timetable/analytics-service:latest|image: $DOCKER_USERNAME/dagito-analytics-service:latest|g" k8s/analytics-service/analytics-service-deployment.yaml

# Update Celery workers
print_status "Updating Celery worker manifests..."
find k8s/ -name "*celery*.yaml" -exec sed -i "s|image: timetable/|image: $DOCKER_USERNAME/dagito-|g" {} \;

print_success "All Kubernetes manifests updated successfully!"

print_status "Updated image references:"
echo "  - hust001/dagito-auth-service:latest"
echo "  - hust001/dagito-user-service:latest"
echo "  - hust001/dagito-preference-service:latest"
echo "  - hust001/dagito-timetable-service:latest"
echo "  - hust001/dagito-notification-service:latest"
echo "  - hust001/dagito-analytics-service:latest"

print_status "You can now deploy to Kubernetes using:"
echo "  kubectl apply -f k8s/ -n dagito"
