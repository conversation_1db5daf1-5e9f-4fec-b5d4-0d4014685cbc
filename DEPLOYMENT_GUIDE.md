# DAGITO Microservices Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the DAGITO Timetable Management System using the Docker images available on DockerHub under the username `hust001`.

## Prerequisites

### For Docker Compose Deployment
- Docker Engine 20.10+
- Docker Compose 2.0+
- 8GB+ RAM
- 20GB+ free disk space

### For Kubernetes Deployment
- Kubernetes cluster (1.20+)
- kubectl configured
- 16GB+ RAM across nodes
- 50GB+ storage

## Quick Start (Docker Compose)

### 1. Clone and Navigate
```bash
git clone <your-repo-url>
cd dagito
```

### 2. Configure Environment
```bash
# Copy and edit the production environment file
cp .env.production .env
# Edit .env with your specific configuration (database passwords, email settings, etc.)
```

### 3. Deploy Services
```bash
# Pull latest images and deploy
./deploy-production.sh docker-compose --pull-latest

# Or deploy with clean start
./deploy-production.sh docker-compose --clean --pull-latest
```

### 4. Access the Application
- **Frontend**: http://localhost:3000
- **API Gateway**: http://localhost:8000
- **Individual Services**: 
  - Auth: http://localhost:8001
  - User: http://localhost:8002
  - Preference: http://localhost:8003
  - Timetable: http://localhost:8004
  - Notification: http://localhost:8005
  - Analytics: http://localhost:8006

## Docker Compose Deployment Details

### Services Included
- **MongoDB**: Database (port 27017)
- **Redis**: Cache and message broker (port 6379)
- **Auth Service**: Authentication and authorization (port 8001)
- **User Service**: User management (port 8002)
- **Preference Service**: Preference collection (port 8003)
- **Timetable Service**: Timetable generation (port 8004)
- **Notification Service**: Notifications (port 8005)
- **Analytics Service**: Data analytics (port 8006)
- **Frontend**: React.js application (port 3000)
- **API Gateway**: NGINX reverse proxy (port 8000)
- **Celery Workers**: Background task processing

### Environment Configuration
Key environment variables to configure in `.env`:

```bash
# Security (CHANGE THESE!)
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
INTERNAL_SERVICE_TOKEN=your-internal-service-token-change-this

# Database
MONGODB_URI=***************************************************************************

# Email (Configure for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Domain (Update for production)
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
```

### Management Commands
```bash
# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f auth-service

# Restart a service
docker-compose restart auth-service

# Stop all services
docker-compose down

# Stop and remove volumes (CAUTION: This deletes data)
docker-compose down -v
```

## Kubernetes Deployment

### 1. Update Image References
```bash
# Update all Kubernetes manifests to use DockerHub images
./update-k8s-images.sh
```

### 2. Create Namespace
```bash
kubectl create namespace dagito
```

### 3. Create Secrets
```bash
# Create secrets for sensitive data
kubectl create secret generic timetable-secrets \
  --from-literal=MONGODB_URI="***************************************************************************" \
  --from-literal=REDIS_URL="redis://redis:6379/0" \
  --from-literal=AUTH_SECRET_KEY="your-auth-secret-key" \
  --from-literal=JWT_SECRET_KEY="your-jwt-secret-key" \
  --from-literal=EMAIL_HOST_USER="<EMAIL>" \
  --from-literal=EMAIL_HOST_PASSWORD="your-app-password" \
  -n dagito
```

### 4. Deploy Services
```bash
# Deploy all services
kubectl apply -f k8s/ -n dagito

# Wait for deployments to be ready
kubectl wait --for=condition=available --timeout=300s deployment --all -n dagito
```

### 5. Access Services
```bash
# Get service endpoints
kubectl get services -n dagito

# Port forward for local access (example)
kubectl port-forward svc/api-gateway 8000:80 -n dagito
kubectl port-forward svc/frontend 3000:3000 -n dagito
```

## Production Considerations

### Security
1. **Change Default Passwords**: Update all default passwords in environment files
2. **Use Secrets Management**: Store sensitive data in Kubernetes secrets or Docker secrets
3. **Enable HTTPS**: Configure SSL/TLS certificates
4. **Network Policies**: Implement Kubernetes network policies for service isolation

### Monitoring
1. **Health Checks**: All services include health check endpoints
2. **Logging**: Centralized logging with ELK stack or similar
3. **Metrics**: Prometheus and Grafana for monitoring
4. **Alerts**: Set up alerts for service failures

### Scaling
1. **Horizontal Scaling**: Increase replicas for high-traffic services
2. **Resource Limits**: Configure appropriate CPU and memory limits
3. **Database Scaling**: Consider MongoDB replica sets for production
4. **Load Balancing**: Use external load balancers for production traffic

### Backup
1. **Database Backups**: Regular MongoDB backups
2. **Volume Backups**: Backup persistent volumes
3. **Configuration Backups**: Version control all configuration files

## Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check service logs
docker-compose logs service-name

# Check resource usage
docker stats

# Restart specific service
docker-compose restart service-name
```

#### Database Connection Issues
```bash
# Check MongoDB logs
docker-compose logs mongodb

# Verify connection string
docker-compose exec auth-service python manage.py shell
```

#### Image Pull Issues
```bash
# Verify images exist on DockerHub
docker pull hust001/dagito-auth-service:latest

# Check Docker Hub credentials
docker login
```

### Health Check Endpoints
- Auth Service: `GET /api/health/`
- User Service: `GET /api/health/`
- Preference Service: `GET /api/health/`
- Timetable Service: `GET /api/health/`
- Notification Service: `GET /api/health/`
- Analytics Service: `GET /api/health/`

## Support

### Logs Location
- Docker Compose: `docker-compose logs`
- Kubernetes: `kubectl logs -n dagito`

### Configuration Files
- Docker Compose: `docker-compose.yml`
- Environment: `.env.production`
- Kubernetes: `k8s/` directory

### Scripts
- `build-and-push.sh`: Build and push all images
- `build-single.sh`: Build individual services
- `deploy-production.sh`: Production deployment
- `verify-images.sh`: Verify DockerHub images
- `update-k8s-images.sh`: Update Kubernetes manifests

For additional support, check the service logs and ensure all prerequisites are met.
