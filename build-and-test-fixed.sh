#!/bin/bash

# Build and Test Fixed Docker Images
# Builds the services with fixed requirements and tests them

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

DOCKER_USERNAME="hust001"
TEST_NETWORK="fixed-test-network"

# Clean up function
cleanup() {
    print_status "Cleaning up test containers and network..."
    docker stop test-mongodb test-redis test-service 2>/dev/null || true
    docker rm test-mongodb test-redis test-service 2>/dev/null || true
    docker network rm $TEST_NETWORK 2>/dev/null || true
    print_success "Cleanup completed"
}

# Build a service
build_service() {
    local service_name=$1
    local service_dir=$2
    local image_name="$DOCKER_USERNAME/dagito-$service_name:latest"
    
    print_status "Building $service_name..."
    echo "=================================="
    
    if [ ! -d "$service_dir" ]; then
        print_error "Service directory $service_dir not found"
        return 1
    fi
    
    cd "$service_dir"
    
    if docker build -t "$image_name" .; then
        print_success "✓ Successfully built $image_name"
        cd ..
        return 0
    else
        print_error "✗ Failed to build $image_name"
        cd ..
        return 1
    fi
}

# Test a service
test_service() {
    local service_name=$1
    local image_name="$DOCKER_USERNAME/dagito-$service_name:latest"
    local port=$2
    local test_duration=${3:-60}
    
    print_status "Testing $service_name..."
    echo "=================================="
    
    # Setup infrastructure if not already running
    if ! docker ps --filter "name=test-mongodb" --format "{{.Names}}" | grep -q "test-mongodb"; then
        print_status "Starting test infrastructure..."
        
        # Create test network
        docker network create $TEST_NETWORK 2>/dev/null || true
        
        # Start MongoDB
        docker run -d \
            --name test-mongodb \
            --network $TEST_NETWORK \
            -e MONGO_INITDB_ROOT_USERNAME=admin \
            -e MONGO_INITDB_ROOT_PASSWORD=password123 \
            mongo:6.0 >/dev/null
        
        # Start Redis
        docker run -d \
            --name test-redis \
            --network $TEST_NETWORK \
            redis:7-alpine >/dev/null
        
        # Wait for services to be ready
        print_status "Waiting for infrastructure to be ready..."
        sleep 15
    fi
    
    # Start container
    print_status "Starting $service_name container..."
    local container_id=$(docker run -d \
        --name test-service \
        --network $TEST_NETWORK \
        -p "$port:$port" \
        -e DEBUG=True \
        -e MONGODB_URI=******************************************************************************** \
        -e REDIS_URL=redis://test-redis:6379/0 \
        -e SECRET_KEY=test-secret-key-for-testing \
        -e JWT_SECRET_KEY=test-jwt-secret-key-for-testing \
        -e ALLOWED_HOSTS=localhost,127.0.0.1,$service_name \
        -e AUTH_SERVICE_URL=http://localhost:8001 \
        -e USER_SERVICE_URL=http://localhost:8002 \
        -e PREFERENCE_SERVICE_URL=http://localhost:8003 \
        -e TIMETABLE_SERVICE_URL=http://localhost:8004 \
        -e NOTIFICATION_SERVICE_URL=http://localhost:8005 \
        -e ANALYTICS_SERVICE_URL=http://localhost:8006 \
        "$image_name")
    
    if [ $? -ne 0 ]; then
        print_error "Failed to start container for $service_name"
        return 1
    fi
    
    print_success "Container started: $container_id"
    
    # Wait for container to initialize
    print_status "Waiting for service to initialize ($test_duration seconds)..."
    sleep $test_duration
    
    # Check if container is still running
    if ! docker ps --filter "id=$container_id" --format "{{.ID}}" | grep -q "$container_id"; then
        print_error "Container stopped unexpectedly"
        print_status "Container logs:"
        docker logs "$container_id" | tail -30
        docker rm -f "$container_id" 2>/dev/null
        return 1
    fi
    
    # Test basic connectivity
    print_status "Testing basic connectivity on port $port..."
    local connectivity_status="FAILED"
    if nc -z localhost $port 2>/dev/null; then
        connectivity_status="SUCCESS"
        print_success "Port $port is accessible"
    else
        print_warning "Port $port is not accessible"
    fi
    
    # Show container stats
    print_status "Container resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" "$container_id"
    
    # Show recent logs
    print_status "Recent container logs (last 15 lines):"
    docker logs --tail=15 "$container_id"
    
    # Stop and remove container
    print_status "Stopping container..."
    docker stop "$container_id" >/dev/null
    docker rm "$container_id" >/dev/null
    
    # Determine overall status
    local overall_status="PARTIAL"
    if [ "$connectivity_status" = "SUCCESS" ]; then
        overall_status="SUCCESS"
    else
        overall_status="FAILED"
    fi
    
    print_success "$service_name test completed - Status: $overall_status"
    echo
    
    if [ "$overall_status" = "SUCCESS" ]; then
        return 0
    else
        return 1
    fi
}

# Main function
main() {
    print_status "Building and testing fixed DAGITO microservices"
    print_status "Docker Hub Username: $DOCKER_USERNAME"
    echo
    
    # Check prerequisites
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v nc &> /dev/null; then
        print_error "netcat (nc) is not installed"
        exit 1
    fi
    
    # Services to build and test
    declare -A services=(
        ["timetable-service"]="timetable-service:8004"
        ["auth-service"]="auth-service:8001"
        ["user-service"]="user-service:8002"
        ["preference-service"]="preference-service:8003"
        ["notification-service"]="notification-service:8005"
        ["analytics-service"]="analytics-service:8006"
    )
    
    # Build results tracking
    local built_services=()
    local failed_builds=()
    local passed_tests=()
    local failed_tests=()
    
    echo "🔨 BUILDING SERVICES"
    echo "===================="
    
    for service_name in "${!services[@]}"; do
        service_info="${services[$service_name]}"
        service_dir="${service_info%:*}"
        
        if build_service "$service_name" "$service_dir"; then
            built_services+=("$service_name")
        else
            failed_builds+=("$service_name")
        fi
    done
    
    echo
    echo "🧪 TESTING SUCCESSFULLY BUILT SERVICES"
    echo "======================================="
    
    for service_name in "${built_services[@]}"; do
        service_info="${services[$service_name]}"
        port="${service_info#*:}"
        
        if test_service "$service_name" "$port" 60; then
            passed_tests+=("$service_name")
        else
            failed_tests+=("$service_name")
        fi
    done
    
    # Cleanup
    cleanup
    
    # Final results
    echo
    echo "🏁 BUILD AND TEST RESULTS SUMMARY"
    echo "=================================="
    print_status "Total services: ${#services[@]}"
    print_success "Successfully built: ${#built_services[@]}"
    print_success "Passed tests: ${#passed_tests[@]}"
    
    if [ ${#failed_builds[@]} -gt 0 ]; then
        print_error "Failed builds: ${#failed_builds[@]}"
        echo "  Failed to build: ${failed_builds[*]}"
    fi
    
    if [ ${#failed_tests[@]} -gt 0 ]; then
        print_error "Failed tests: ${#failed_tests[@]}"
        echo "  Failed tests: ${failed_tests[*]}"
    fi
    
    echo
    if [ ${#failed_builds[@]} -eq 0 ] && [ ${#failed_tests[@]} -eq 0 ]; then
        print_success "🎉 All services built and tested successfully!"
    else
        print_warning "⚠️  Some services had issues. Check the logs above for details."
    fi
    
    echo
    print_status "Successfully built and tested services:"
    for service in "${passed_tests[@]}"; do
        echo "  ✓ $DOCKER_USERNAME/dagito-$service:latest"
    done
    
    if [ ${#failed_builds[@]} -gt 0 ] || [ ${#failed_tests[@]} -gt 0 ]; then
        echo
        print_status "Services that need attention:"
        for service in "${failed_builds[@]}"; do
            echo "  ✗ $service (build failed)"
        done
        for service in "${failed_tests[@]}"; do
            echo "  ⚠ $service (test failed)"
        done
    fi
}

# Handle script interruption
trap cleanup EXIT INT TERM

# Run main function
main "$@"
