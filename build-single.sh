#!/bin/bash

# Build Single Service Script for DAGITO Microservices
# Usage: ./build-single.sh <service-name> [test] [push]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

DOCKER_USERNAME="hust001"

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Service configurations
declare -A SERVICES
SERVICES[auth]="auth-service:dagito-auth-service:8001:/api/health/"
SERVICES[user]="user-service:dagito-user-service:8002:/api/health/"
SERVICES[preference]="preference-service:dagito-preference-service:8003:/api/health/"
SERVICES[timetable]="timetable-service:dagito-timetable-service:8004:/api/health/"
SERVICES[notification]="notification-service:dagito-notification-service:8005:/api/health/"
SERVICES[analytics]="analytics-service:dagito-analytics-service:8006:/api/health/"
SERVICES[frontend]="frontend:dagito-frontend:3000:"

usage() {
    echo "Usage: $0 <service-name> [test] [push]"
    echo
    echo "Available services:"
    for service in "${!SERVICES[@]}"; do
        echo "  - $service"
    done
    echo
    echo "Options:"
    echo "  test  - Run container test after building"
    echo "  push  - Push to Docker Hub after building (and testing if specified)"
    echo
    echo "Examples:"
    echo "  $0 auth                    # Build auth service only"
    echo "  $0 auth test               # Build and test auth service"
    echo "  $0 auth test push          # Build, test, and push auth service"
    echo "  $0 frontend push           # Build and push frontend service"
}

build_service() {
    local service_dir=$1
    local image_name=$2
    
    print_status "Building $DOCKER_USERNAME/$image_name:latest..."
    
    if [ ! -d "$service_dir" ]; then
        print_error "Directory $service_dir does not exist!"
        return 1
    fi
    
    if [ ! -f "$service_dir/Dockerfile" ]; then
        print_error "Dockerfile not found in $service_dir!"
        return 1
    fi
    
    docker build -t "$DOCKER_USERNAME/$image_name:latest" "$service_dir"
    
    if [ $? -eq 0 ]; then
        print_success "Successfully built $DOCKER_USERNAME/$image_name:latest"
        return 0
    else
        print_error "Failed to build $image_name"
        return 1
    fi
}

test_service() {
    local image_name=$1
    local port=$2
    local health_endpoint=$3
    
    print_status "Testing $DOCKER_USERNAME/$image_name:latest..."
    
    container_id=$(docker run -d -p "$port:$port" "$DOCKER_USERNAME/$image_name:latest")
    
    if [ $? -ne 0 ]; then
        print_error "Failed to start container for $image_name"
        return 1
    fi
    
    print_status "Container started with ID: $container_id"
    print_status "Waiting for container to be ready..."
    sleep 10
    
    if ! docker ps | grep -q "$container_id"; then
        print_error "Container $container_id is not running"
        docker logs "$container_id"
        docker rm -f "$container_id" 2>/dev/null
        return 1
    fi
    
    if [ -n "$health_endpoint" ]; then
        print_status "Testing health endpoint: http://localhost:$port$health_endpoint"
        if curl -f "http://localhost:$port$health_endpoint" >/dev/null 2>&1; then
            print_success "Health check passed"
        else
            print_warning "Health check failed (this might be expected if dependencies are not available)"
        fi
    fi
    
    docker stop "$container_id" >/dev/null
    docker rm "$container_id" >/dev/null
    
    print_success "Test completed for $image_name"
    return 0
}

push_service() {
    local image_name=$1
    
    print_status "Pushing $DOCKER_USERNAME/$image_name:latest to Docker Hub..."
    
    docker push "$DOCKER_USERNAME/$image_name:latest"
    
    if [ $? -eq 0 ]; then
        print_success "Successfully pushed $DOCKER_USERNAME/$image_name:latest"
        return 0
    else
        print_error "Failed to push $image_name"
        return 1
    fi
}

main() {
    if [ $# -lt 1 ]; then
        usage
        exit 1
    fi
    
    local service_key=$1
    local do_test=false
    local do_push=false
    
    # Parse arguments
    for arg in "$@"; do
        case $arg in
            test) do_test=true ;;
            push) do_push=true ;;
        esac
    done
    
    # Check if service exists
    if [ -z "${SERVICES[$service_key]}" ]; then
        print_error "Unknown service: $service_key"
        usage
        exit 1
    fi
    
    # Parse service configuration
    IFS=':' read -r service_dir image_name port health_endpoint <<< "${SERVICES[$service_key]}"
    
    print_status "Processing $service_key service..."
    print_status "Service directory: $service_dir"
    print_status "Image name: $DOCKER_USERNAME/$image_name:latest"
    print_status "Port: $port"
    echo
    
    # Build
    if ! build_service "$service_dir" "$image_name"; then
        exit 1
    fi
    
    # Test if requested
    if [ "$do_test" = true ]; then
        if ! test_service "$image_name" "$port" "$health_endpoint"; then
            print_warning "Test failed, but continuing..."
        fi
    fi
    
    # Push if requested
    if [ "$do_push" = true ]; then
        if ! push_service "$image_name"; then
            exit 1
        fi
    fi
    
    print_success "Completed processing $service_key service"
}

main "$@"
