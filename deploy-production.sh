#!/bin/bash

# DAGITO Production Deployment Script
# Supports Docker Compose and Kubernetes deployment with DockerHub images

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
DOCKER_USERNAME="hust001"
NAMESPACE="dagito"

usage() {
    echo "Usage: $0 <deployment-type> [options]"
    echo
    echo "Deployment Types:"
    echo "  docker-compose    Deploy using Docker Compose"
    echo "  kubernetes        Deploy to Kubernetes cluster"
    echo "  local-test        Quick local test deployment"
    echo
    echo "Options:"
    echo "  --pull-latest     Pull latest images before deployment"
    echo "  --clean           Clean up existing deployment first"
    echo "  --logs            Show logs after deployment"
    echo "  --help            Show this help message"
    echo
    echo "Examples:"
    echo "  $0 docker-compose --pull-latest"
    echo "  $0 kubernetes --clean"
    echo "  $0 local-test --logs"
}

check_prerequisites() {
    local deployment_type=$1
    
    print_status "Checking prerequisites for $deployment_type deployment..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if [ "$deployment_type" = "docker-compose" ] || [ "$deployment_type" = "local-test" ]; then
        if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
            print_error "Docker Compose is not installed"
            exit 1
        fi
    fi
    
    if [ "$deployment_type" = "kubernetes" ]; then
        if ! command -v kubectl &> /dev/null; then
            print_error "kubectl is not installed or not in PATH"
            exit 1
        fi
        
        # Check if kubectl can connect to cluster
        if ! kubectl cluster-info &> /dev/null; then
            print_error "Cannot connect to Kubernetes cluster"
            exit 1
        fi
    fi
    
    print_success "Prerequisites check passed"
}

pull_latest_images() {
    print_status "Pulling latest images from DockerHub..."
    
    local images=(
        "dagito-auth-service:latest"
        "dagito-user-service:latest"
        "dagito-preference-service:latest"
        "dagito-timetable-service:latest"
        "dagito-notification-service:latest"
        "dagito-analytics-service:latest"
        "dagito-frontend:latest"
    )
    
    for image in "${images[@]}"; do
        print_status "Pulling $DOCKER_USERNAME/$image..."
        docker pull "$DOCKER_USERNAME/$image"
    done
    
    print_success "All images pulled successfully"
}

deploy_docker_compose() {
    local clean=$1
    local show_logs=$2
    
    print_status "Deploying DAGITO using Docker Compose..."
    
    if [ "$clean" = true ]; then
        print_status "Cleaning up existing deployment..."
        docker-compose down -v --remove-orphans 2>/dev/null || true
    fi
    
    # Create necessary directories
    mkdir -p logs
    
    # Deploy services
    print_status "Starting services..."
    docker-compose up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_service_health_docker
    
    if [ "$show_logs" = true ]; then
        print_status "Showing logs (Ctrl+C to exit)..."
        docker-compose logs -f
    fi
}

check_service_health_docker() {
    print_status "Checking service health..."
    
    local services=(
        "auth-service:8001"
        "user-service:8002"
        "preference-service:8003"
        "timetable-service:8004"
        "notification-service:8005"
        "analytics-service:8006"
        "frontend:3000"
        "api-gateway:8000"
    )
    
    local healthy_services=0
    local total_services=${#services[@]}
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service port <<< "$service_info"
        
        if curl -f "http://localhost:$port/health" >/dev/null 2>&1 || \
           curl -f "http://localhost:$port/api/health/" >/dev/null 2>&1 || \
           curl -f "http://localhost:$port/" >/dev/null 2>&1; then
            print_success "✓ $service is healthy"
            ((healthy_services++))
        else
            print_warning "⚠ $service health check failed"
        fi
    done
    
    print_status "Health check: $healthy_services/$total_services services healthy"
}

show_deployment_info() {
    local deployment_type=$1
    
    echo
    print_success "DAGITO deployment completed successfully!"
    echo
    print_status "Deployment Information:"
    echo "  Type: $deployment_type"
    echo "  Namespace: $NAMESPACE (Kubernetes only)"
    echo
    print_status "Access URLs:"
    echo "  Frontend: http://localhost:3000"
    echo "  API Gateway: http://localhost:8000"
    echo "  Auth Service: http://localhost:8001"
    echo "  User Service: http://localhost:8002"
    echo "  Preference Service: http://localhost:8003"
    echo "  Timetable Service: http://localhost:8004"
    echo "  Notification Service: http://localhost:8005"
    echo "  Analytics Service: http://localhost:8006"
    echo
    print_status "Management Commands:"
    if [ "$deployment_type" = "docker-compose" ] || [ "$deployment_type" = "local-test" ]; then
        echo "  View logs: docker-compose logs -f"
        echo "  Stop services: docker-compose down"
        echo "  Restart service: docker-compose restart <service-name>"
    elif [ "$deployment_type" = "kubernetes" ]; then
        echo "  View logs: kubectl logs -f -l app=dagito -n $NAMESPACE"
        echo "  View pods: kubectl get pods -n $NAMESPACE"
        echo "  Delete deployment: kubectl delete namespace $NAMESPACE"
    fi
}

main() {
    if [ $# -lt 1 ]; then
        usage
        exit 1
    fi
    
    local deployment_type=$1
    shift
    
    local pull_latest=false
    local clean=false
    local show_logs=false
    
    # Parse options
    while [[ $# -gt 0 ]]; do
        case $1 in
            --pull-latest)
                pull_latest=true
                shift
                ;;
            --clean)
                clean=true
                shift
                ;;
            --logs)
                show_logs=true
                shift
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    case $deployment_type in
        docker-compose|local-test)
            check_prerequisites "$deployment_type"
            if [ "$pull_latest" = true ]; then
                pull_latest_images
            fi
            deploy_docker_compose "$clean" "$show_logs"
            show_deployment_info "$deployment_type"
            ;;
        kubernetes)
            print_error "Kubernetes deployment not implemented in this version"
            print_status "Please use the existing k8s manifests manually"
            exit 1
            ;;
        *)
            print_error "Unknown deployment type: $deployment_type"
            usage
            exit 1
            ;;
    esac
}

main "$@"
