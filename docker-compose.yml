version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: timetable_mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: timetable_system
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - timetable_network

  # Redis for caching and Celery
  redis:
    image: redis:7-alpine
    container_name: timetable_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - timetable_network

  # Authentication Service
  auth-service:
    image: hust001/dagito-auth-service:latest
    container_name: timetable_auth_service
    restart: unless-stopped
    ports:
      - "8001:8001"
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=False
      - SECRET_KEY=auth-service-secret-key-prod
      - ALLOWED_HOSTS=localhost,127.0.0.1,auth-service,api-gateway
    volumes:
      - auth_static:/app/staticfiles
      - auth_media:/app/media
    depends_on:
      - mongodb
      - redis
    networks:
      - timetable_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # User Management Service
  user-service:
    image: hust001/dagito-user-service:latest
    container_name: timetable_user_service
    restart: unless-stopped
    ports:
      - "8002:8002"
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - AUTH_SERVICE_URL=http://auth-service:8001
      - DEBUG=False
      - SECRET_KEY=user-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - AUTH_SERVICE_INTERNAL_TOKEN=internal-service-token
      - ALLOWED_HOSTS=localhost,127.0.0.1,user-service,api-gateway
    volumes:
      - user_static:/app/staticfiles
      - user_media:/app/media
    depends_on:
      - mongodb
      - redis
      - auth-service
    networks:
      - timetable_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Preference Collection Service
  preference-service:
    image: hust001/dagito-preference-service:latest
    container_name: timetable_preference_service
    restart: unless-stopped
    ports:
      - "8003:8003"
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - TIMETABLE_SERVICE_URL=http://timetable-service:8004
      - DEBUG=False
      - SECRET_KEY=preference-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - INTERNAL_SERVICE_TOKEN=internal-service-token
      - ALLOWED_HOSTS=localhost,127.0.0.1,preference-service,api-gateway
      - PREFERENCE_VALIDATION_ENABLED=True
      - PREFERENCE_CONFLICT_DETECTION=True
      - PREFERENCE_AUTO_SAVE_INTERVAL=30
      - DEFAULT_TIME_SLOT_DURATION=60
      - WORKING_HOURS_START=08:00
      - WORKING_HOURS_END=18:00
      - WORKING_DAYS=monday,tuesday,wednesday,thursday,friday
    volumes:
      - preference_static:/app/staticfiles
      - preference_media:/app/media
    depends_on:
      - mongodb
      - redis
      - auth-service
      - user-service
    networks:
      - timetable_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Preference Service Celery Worker
  preference-celery-worker:
    image: hust001/dagito-preference-service:latest
    container_name: timetable_preference_celery_worker
    restart: unless-stopped
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - TIMETABLE_SERVICE_URL=http://timetable-service:8004
      - DEBUG=False
      - SECRET_KEY=preference-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - INTERNAL_SERVICE_TOKEN=internal-service-token
    depends_on:
      - mongodb
      - redis
      - preference-service
    networks:
      - timetable_network
    command: celery -A preference_service worker --loglevel=info

  # Preference Service Celery Beat
  preference-celery-beat:
    image: hust001/dagito-preference-service:latest
    container_name: timetable_preference_celery_beat
    restart: unless-stopped
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - TIMETABLE_SERVICE_URL=http://timetable-service:8004
      - DEBUG=False
      - SECRET_KEY=preference-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - INTERNAL_SERVICE_TOKEN=internal-service-token
    depends_on:
      - mongodb
      - redis
      - preference-service
    networks:
      - timetable_network
    command: celery -A preference_service beat --loglevel=info

  # Timetable Generation Service
  timetable-service:
    image: hust001/dagito-timetable-service:latest
    container_name: timetable_timetable_service
    restart: unless-stopped
    ports:
      - "8004:8004"
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - PREFERENCE_SERVICE_URL=http://preference-service:8003
      - NOTIFICATION_SERVICE_URL=http://notification-service:8005
      - DEBUG=False
      - SECRET_KEY=timetable-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - INTERNAL_SERVICE_TOKEN=internal-service-token
      - ALLOWED_HOSTS=localhost,127.0.0.1,timetable-service,api-gateway
      - GENETIC_ALGORITHM_POPULATION_SIZE=100
      - GENETIC_ALGORITHM_GENERATIONS=50
      - GENETIC_ALGORITHM_MUTATION_RATE=0.1
      - GENETIC_ALGORITHM_CROSSOVER_RATE=0.8
      - TIMETABLE_OPTIMIZATION_TIMEOUT=300
      - CONFLICT_DETECTION_ENABLED=True
      - AUTO_CONFLICT_RESOLUTION=True
    volumes:
      - timetable_static:/app/staticfiles
      - timetable_media:/app/media
    depends_on:
      - mongodb
      - redis
      - auth-service
      - user-service
      - preference-service
    networks:
      - timetable_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Timetable Service Celery Worker
  timetable-celery-worker:
    image: hust001/dagito-timetable-service:latest
    container_name: timetable_timetable_celery_worker
    restart: unless-stopped
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - PREFERENCE_SERVICE_URL=http://preference-service:8003
      - NOTIFICATION_SERVICE_URL=http://notification-service:8005
      - DEBUG=False
      - SECRET_KEY=timetable-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - INTERNAL_SERVICE_TOKEN=internal-service-token
    depends_on:
      - mongodb
      - redis
      - timetable-service
    networks:
      - timetable_network
    command: celery -A timetable_service worker --loglevel=info --concurrency=2

  # Timetable Service Celery Beat
  timetable-celery-beat:
    image: hust001/dagito-timetable-service:latest
    container_name: timetable_timetable_celery_beat
    restart: unless-stopped
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - PREFERENCE_SERVICE_URL=http://preference-service:8003
      - NOTIFICATION_SERVICE_URL=http://notification-service:8005
      - DEBUG=False
      - SECRET_KEY=timetable-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - INTERNAL_SERVICE_TOKEN=internal-service-token
    depends_on:
      - mongodb
      - redis
      - timetable-service
    networks:
      - timetable_network
    command: celery -A timetable_service beat --loglevel=info

  # Notification Service
  notification-service:
    image: hust001/dagito-notification-service:latest
    container_name: timetable_notification_service
    restart: unless-stopped
    ports:
      - "8005:8005"
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - TIMETABLE_SERVICE_URL=http://timetable-service:8004
      - DEBUG=False
      - SECRET_KEY=notification-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - INTERNAL_SERVICE_TOKEN=internal-service-token
      - ALLOWED_HOSTS=localhost,127.0.0.1,notification-service,api-gateway
      - EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
      - EMAIL_HOST=smtp.gmail.com
      - EMAIL_PORT=587
      - EMAIL_USE_TLS=True
      - EMAIL_HOST_USER=<EMAIL>
      - EMAIL_HOST_PASSWORD=your-app-password
      - DEFAULT_FROM_EMAIL=DAGITO System <<EMAIL>>
      - WEBSOCKET_ENABLED=True
      - SMS_ENABLED=False
    volumes:
      - notification_static:/app/staticfiles
      - notification_media:/app/media
    depends_on:
      - mongodb
      - redis
      - auth-service
      - user-service
    networks:
      - timetable_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Notification Service Celery Worker
  notification-celery-worker:
    image: hust001/dagito-notification-service:latest
    container_name: timetable_notification_celery_worker
    restart: unless-stopped
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - TIMETABLE_SERVICE_URL=http://timetable-service:8004
      - DEBUG=False
      - SECRET_KEY=notification-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - INTERNAL_SERVICE_TOKEN=internal-service-token
      - EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
      - EMAIL_HOST=smtp.gmail.com
      - EMAIL_PORT=587
      - EMAIL_USE_TLS=True
      - EMAIL_HOST_USER=<EMAIL>
      - EMAIL_HOST_PASSWORD=your-app-password
      - DEFAULT_FROM_EMAIL=DAGITO System <<EMAIL>>
    depends_on:
      - mongodb
      - redis
      - notification-service
    networks:
      - timetable_network
    command: celery -A notification_service worker --loglevel=info

  # Analytics Service
  analytics-service:
    image: hust001/dagito-analytics-service:latest
    container_name: timetable_analytics_service
    restart: unless-stopped
    ports:
      - "8006:8006"
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - TIMETABLE_SERVICE_URL=http://timetable-service:8004
      - PREFERENCE_SERVICE_URL=http://preference-service:8003
      - DEBUG=False
      - SECRET_KEY=analytics-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - INTERNAL_SERVICE_TOKEN=internal-service-token
      - ALLOWED_HOSTS=localhost,127.0.0.1,analytics-service,api-gateway
      - ANALYTICS_DATA_RETENTION_DAYS=365
      - REPORT_GENERATION_ENABLED=True
      - REAL_TIME_ANALYTICS=True
    volumes:
      - analytics_static:/app/staticfiles
      - analytics_media:/app/media
    depends_on:
      - mongodb
      - redis
      - auth-service
      - user-service
      - timetable-service
      - preference-service
    networks:
      - timetable_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8006/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Analytics Service Celery Worker
  analytics-celery-worker:
    image: hust001/dagito-analytics-service:latest
    container_name: timetable_analytics_celery_worker
    restart: unless-stopped
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - TIMETABLE_SERVICE_URL=http://timetable-service:8004
      - PREFERENCE_SERVICE_URL=http://preference-service:8003
      - DEBUG=False
      - SECRET_KEY=analytics-service-secret-key-prod
      - JWT_SECRET_KEY=jwt-secret-key-production
      - INTERNAL_SERVICE_TOKEN=internal-service-token
    depends_on:
      - mongodb
      - redis
      - analytics-service
    networks:
      - timetable_network
    command: celery -A analytics_service worker --loglevel=info

  # Frontend (React.js)
  frontend:
    image: hust001/dagito-frontend:latest
    container_name: timetable_frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8000/api
      - REACT_APP_WS_BASE_URL=ws://localhost:8000/ws
      - REACT_APP_ENV=production
      - REACT_APP_AUTH_SERVICE_URL=http://localhost:8000/api/auth
      - REACT_APP_USER_SERVICE_URL=http://localhost:8000/api/users
      - REACT_APP_PREFERENCE_SERVICE_URL=http://localhost:8000/api/preferences
      - REACT_APP_TIMETABLE_SERVICE_URL=http://localhost:8000/api/timetables
      - REACT_APP_NOTIFICATION_SERVICE_URL=http://localhost:8000/api/notifications
      - REACT_APP_ANALYTICS_SERVICE_URL=http://localhost:8000/api/analytics
    depends_on:
      - api-gateway
    networks:
      - timetable_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway (NGINX)
  api-gateway:
    image: nginx:alpine
    container_name: timetable_api_gateway
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./api-gateway/nginx.conf:/etc/nginx/nginx.conf:ro
      - auth_static:/var/www/auth/static:ro
      - user_static:/var/www/user/static:ro
      - preference_static:/var/www/preference/static:ro
      - timetable_static:/var/www/timetable/static:ro
      - notification_static:/var/www/notification/static:ro
      - analytics_static:/var/www/analytics/static:ro
    depends_on:
      - auth-service
      - user-service
      - preference-service
      - timetable-service
      - notification-service
      - analytics-service
    networks:
      - timetable_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  auth_static:
    driver: local
  auth_media:
    driver: local
  user_static:
    driver: local
  user_media:
    driver: local
  preference_static:
    driver: local
  preference_media:
    driver: local
  timetable_static:
    driver: local
  timetable_media:
    driver: local
  notification_static:
    driver: local
  notification_media:
    driver: local
  analytics_static:
    driver: local
  analytics_media:
    driver: local

networks:
  timetable_network:
    driver: bridge
