# Data Analytics Service Environment Configuration

# Django Settings
DEBUG=True
SECRET_KEY=analytics-service-secret-key-development
ALLOWED_HOSTS=localhost,127.0.0.1,analytics-service

# Database Configuration
MONGODB_URI=*****************************************************************************
MONGODB_DB_NAME=timetable_system

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# JWT Configuration
JWT_SECRET_KEY=jwt-secret-key-development
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=7
JWT_ALGORITHM=HS256

# Service URLs
USER_SERVICE_URL=http://user-service:8002
AUTH_SERVICE_URL=http://auth-service:8001
PREFERENCE_SERVICE_URL=http://preference-service:8003
TIMETABLE_SERVICE_URL=http://timetable-service:8004
NOTIFICATION_SERVICE_URL=http://notification-service:8005

# Internal service authentication
INTERNAL_SERVICE_TOKEN=internal-service-token

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Analytics Service Specific Settings
ANALYTICS_DATA_RETENTION_DAYS=365
ANALYTICS_CACHE_TIMEOUT=3600
REPORT_GENERATION_TIMEOUT=300

# Data Processing Settings
PANDAS_CHUNK_SIZE=10000
MAX_CONCURRENT_REPORTS=5

# Visualization Settings
MATPLOTLIB_BACKEND=Agg
CHART_DEFAULT_WIDTH=12
CHART_DEFAULT_HEIGHT=8
CHART_DPI=300

# Report Settings
REPORT_RETENTION_DAYS=30

# Dashboard Settings
DASHBOARD_REFRESH_INTERVAL=300
DASHBOARD_CACHE_TIMEOUT=600

# Logging Level
LOG_LEVEL=INFO

# Performance Settings
CELERY_WORKER_CONCURRENCY=4
CELERY_WORKER_MAX_TASKS_PER_CHILD=1000

# Security Settings (Production)
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY
