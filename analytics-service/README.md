# Data Analytics Service

A comprehensive microservice for data analytics, reporting, and dashboard insights for the timetable management system. This service processes data from all other microservices to provide valuable insights, generate reports, and power admin dashboards.

## Overview

The Data Analytics Service provides:

- **Timetable Efficiency Analysis** with satisfaction metrics
- **Preference Trend Analysis** and pattern recognition
- **Conflict Detection and Analysis** with resolution insights
- **Automated Report Generation** in multiple formats (PDF, Excel, CSV, JSON, HTML)
- **Real-time Admin Dashboard** with key performance indicators
- **Data Visualization** with Chart.js integration and Matplotlib charts
- **Scheduled Reporting** with email delivery
- **Performance Monitoring** and system health tracking

## Technology Stack

- **Framework**: Django REST Framework
- **Database**: MongoDB (shared with other services)
- **Data Processing**: Pandas, NumPy, SciPy, Scikit-learn
- **Visualization**: Mat<PERSON>lotlib, Seaborn, Plotly
- **Report Generation**: ReportLab (PDF), OpenPyXL (Excel)
- **Task Queue**: Celery with Redis
- **Caching**: Redis for dashboard data
- **Authentication**: JWT tokens from Authentication Service
- **API Documentation**: Swagger/OpenAPI

## Key Features

### 📊 Analytics Engine
- **Timetable Efficiency Analysis**: Overall efficiency scoring, conflict rate analysis, resource utilization tracking
- **Satisfaction Metrics**: Lecturer and student satisfaction tracking, preference fulfillment analysis
- **Preference Trends**: Time slot preferences, workload distribution analysis, seasonal trend detection
- **Conflict Analysis**: Conflict pattern recognition, hotspot identification, resolution recommendations
- **Usage Patterns**: System usage analytics, peak hour analysis, user behavior tracking

### 📈 Dashboard & Insights
- **Real-time Dashboard**: Live metrics, trend visualization, performance indicators
- **Admin Insights**: System health monitoring, efficiency trends, conflict hotspots
- **Interactive Charts**: Chart.js integration for frontend, Matplotlib for backend
- **KPI Tracking**: Key performance indicators with targets and alerts
- **Customizable Widgets**: Configurable dashboard components

### 📋 Report Generation
- **Multiple Formats**: PDF, Excel, CSV, JSON, HTML reports
- **Template System**: Customizable report templates with Jinja2
- **Scheduled Reports**: Automated daily/weekly/monthly reports
- **Bulk Generation**: Batch report processing for multiple recipients
- **Email Integration**: Automatic report delivery via notification service

### 🔍 Data Processing
- **Pandas Integration**: Efficient data manipulation and analysis
- **Statistical Analysis**: Trend analysis, correlation detection, forecasting
- **Machine Learning**: Pattern recognition, anomaly detection (future enhancement)
- **Data Validation**: Input validation and data quality checks
- **Performance Optimization**: Chunked processing for large datasets

## API Endpoints

### Analytics
- `GET /api/analytics/timetables/` - Timetable analytics data
- `GET /api/analytics/timetables/efficiency-summary/` - Efficiency summary
- `GET /api/analytics/timetables/trends/` - Efficiency trends
- `POST /api/analytics/timetables/{id}/reanalyze/` - Trigger re-analysis
- `GET /api/analytics/satisfaction/` - Satisfaction metrics
- `GET /api/analytics/satisfaction/by-entity-type/` - Satisfaction by entity
- `GET /api/analytics/preferences/` - Preference trends
- `GET /api/analytics/conflicts/` - Conflict analysis
- `POST /api/analytics/trigger-update/` - Trigger analytics update

### Reports
- `GET /api/reports/templates/` - Report templates
- `POST /api/reports/templates/` - Create template
- `GET /api/reports/reports/` - Generated reports
- `POST /api/reports/reports/` - Create report
- `GET /api/reports/reports/{id}/download/` - Download report
- `POST /api/reports/reports/{id}/share/` - Share report
- `GET /api/reports/schedules/` - Scheduled reports
- `POST /api/reports/generate-custom/` - Generate custom report

### Dashboard
- `GET /api/dashboard/overview/` - Dashboard overview
- `GET /api/dashboard/efficiency-trends/` - Efficiency trends
- `GET /api/dashboard/satisfaction-breakdown/` - Satisfaction breakdown
- `GET /api/dashboard/conflict-insights/` - Conflict insights
- `GET /api/dashboard/preference-analytics/` - Preference analytics
- `GET /api/dashboard/system-performance/` - System performance
- `GET /api/dashboard/chart-data/` - Chart data for visualizations
- `GET /api/dashboard/widgets/` - Dashboard widgets data

## Environment Variables

```bash
# Core Settings
DEBUG=False
SECRET_KEY=your-secret-key
ALLOWED_HOSTS=localhost,analytics-service

# Database
MONGODB_URI=*********************************************************
MONGODB_DB_NAME=timetable_system

# Redis & Celery
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Service URLs
USER_SERVICE_URL=http://user-service:8002
PREFERENCE_SERVICE_URL=http://preference-service:8003
TIMETABLE_SERVICE_URL=http://timetable-service:8004
NOTIFICATION_SERVICE_URL=http://notification-service:8005

# Analytics Settings
ANALYTICS_DATA_RETENTION_DAYS=365
ANALYTICS_CACHE_TIMEOUT=3600
REPORT_GENERATION_TIMEOUT=300

# Data Processing
PANDAS_CHUNK_SIZE=10000
MAX_CONCURRENT_REPORTS=5

# Visualization
MATPLOTLIB_BACKEND=Agg
CHART_DEFAULT_WIDTH=12
CHART_DEFAULT_HEIGHT=8
CHART_DPI=300

# Dashboard
DASHBOARD_REFRESH_INTERVAL=300
DASHBOARD_CACHE_TIMEOUT=600
```

## Docker Deployment

### Build Image
```bash
docker build -t timetable/analytics-service:latest .
```

### Run Service
```bash
docker run -d \
  --name analytics-service \
  -p 8006:8006 \
  --env-file .env \
  timetable/analytics-service:latest
```

### Run Celery Worker
```bash
docker run -d \
  --name analytics-celery-worker \
  --env-file .env \
  timetable/analytics-service:latest \
  celery -A analytics_service worker --loglevel=info
```

## Kubernetes Deployment

```bash
# Deploy service
kubectl apply -f k8s/analytics-service/

# Check status
kubectl get pods -n timetable-system -l app=analytics-service
```

## Development Setup

### Prerequisites
- Python 3.11+
- MongoDB
- Redis
- System packages for data processing and visualization

### Installation
```bash
# Clone repository
git clone <repository-url>
cd analytics-service

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Start development server
python manage.py runserver 0.0.0.0:8006

# In another terminal, start Celery worker
celery -A analytics_service worker --loglevel=info

# In another terminal, start Celery beat
celery -A analytics_service beat --loglevel=info
```

## Usage Examples

### Analyzing Timetable Efficiency
```python
from analytics.services import TimetableAnalyticsService

service = TimetableAnalyticsService()
efficiency_metrics = service.analyze_timetable_efficiency('timetable-uuid')

print(f"Efficiency Score: {efficiency_metrics['overall_efficiency_score']}%")
print(f"Conflict Rate: {efficiency_metrics['conflict_rate']}%")
```

### Generating Reports
```python
from reports.services import ReportGeneratorService

generator = ReportGeneratorService()
result = generator.generate_report(
    template=efficiency_template,
    parameters={'include_charts': True},
    filters={'date_range': {'start': '2024-01-01', 'end': '2024-01-31'}}
)
```

### Dashboard Data
```python
from dashboard.services import DashboardService

dashboard = DashboardService()
overview = dashboard.get_overview_metrics()
trends = dashboard.get_efficiency_trends(days=30)
```

## Testing

### Run Tests
```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test analytics
python manage.py test reports
python manage.py test dashboard

# Run with coverage
pytest --cov=. --cov-report=html
```

### Test Analytics Processing
```bash
# Trigger analytics update
curl -X POST http://localhost:8006/api/analytics/trigger-update/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Check analytics status
curl http://localhost:8006/api/analytics/status/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Integration with Other Services

### Data Collection
The service automatically collects data from:
- **User Service**: User information and roles
- **Preference Service**: User preferences and constraints
- **Timetable Service**: Generated timetables and conflicts
- **Notification Service**: Delivery metrics and user engagement

### Report Delivery
Generated reports can be automatically delivered via:
- **Email**: Through notification service integration
- **Dashboard**: Real-time display of key metrics
- **API**: Programmatic access to analytics data

## Performance Considerations

### Data Processing
- **Chunked Processing**: Large datasets processed in configurable chunks
- **Async Tasks**: Heavy computations run in background via Celery
- **Caching**: Dashboard data cached for improved response times
- **Database Optimization**: Efficient queries with proper indexing

### Scalability
- **Horizontal Scaling**: Multiple worker instances for parallel processing
- **Queue Management**: Separate queues for different task types
- **Resource Management**: Configurable memory and CPU limits
- **Load Balancing**: Multiple service instances behind load balancer

## Monitoring and Logging

### Health Checks
```bash
curl http://localhost:8006/api/health/
```

### Metrics
- Analytics processing performance
- Report generation times
- Dashboard response times
- Data freshness indicators

### Logs
- Application logs: `/app/logs/analytics_service.log`
- Celery logs: Separate worker logs
- Processing metrics: Task execution tracking

## Security Considerations

- **Authentication**: JWT token validation
- **Authorization**: Role-based access control
- **Data Privacy**: Anonymization of sensitive data
- **Input Validation**: Comprehensive data validation
- **Rate Limiting**: API rate limiting for resource protection

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
