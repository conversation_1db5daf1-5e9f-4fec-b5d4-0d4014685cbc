"""
Analytics models for the analytics service.
"""
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from core.models import BaseModel, User
import json


class TimetableAnalytics(BaseModel):
    """
    Model for storing timetable analytics data.
    """
    # Timetable identification
    timetable_id = models.CharField(max_length=255, db_index=True)
    timetable_name = models.CharField(max_length=255)
    academic_year = models.CharField(max_length=20)
    semester = models.CharField(max_length=50)
    department = models.CharField(max_length=100, blank=True)
    
    # Analysis period
    analysis_date = models.DateTimeField(default=timezone.now)
    analysis_period_start = models.DateTimeField()
    analysis_period_end = models.DateTimeField()
    
    # Efficiency metrics
    overall_efficiency_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Overall efficiency score (0-100)"
    )
    
    conflict_count = models.PositiveIntegerField(default=0)
    conflict_rate = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Percentage of entries with conflicts"
    )
    
    # Preference satisfaction
    lecturer_preference_satisfaction = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Lecturer preference satisfaction rate"
    )
    student_preference_satisfaction = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Student preference satisfaction rate"
    )
    
    # Resource utilization
    room_utilization_rate = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Room utilization rate"
    )
    time_slot_utilization = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Time slot utilization rate"
    )
    
    # Workload distribution
    lecturer_workload_balance_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Lecturer workload balance score"
    )
    
    # Additional metrics
    total_courses = models.PositiveIntegerField(default=0)
    total_lecturers = models.PositiveIntegerField(default=0)
    total_students = models.PositiveIntegerField(default=0)
    total_rooms = models.PositiveIntegerField(default=0)
    
    # Detailed analytics data
    detailed_metrics = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'analytics_timetable_analytics'
        ordering = ['-analysis_date']
        indexes = [
            models.Index(fields=['timetable_id', 'analysis_date']),
            models.Index(fields=['academic_year', 'semester']),
            models.Index(fields=['department']),
        ]

    def __str__(self):
        return f"Analytics for {self.timetable_name} - {self.analysis_date.date()}"


class SatisfactionMetric(BaseModel):
    """
    Model for tracking user satisfaction metrics.
    """
    ENTITY_TYPES = [
        ('lecturer', 'Lecturer'),
        ('student', 'Student'),
        ('department', 'Department'),
        ('course', 'Course'),
        ('system', 'System-wide'),
    ]
    
    METRIC_TYPES = [
        ('overall_satisfaction', 'Overall Satisfaction'),
        ('time_preference_satisfaction', 'Time Preference Satisfaction'),
        ('workload_satisfaction', 'Workload Satisfaction'),
        ('room_satisfaction', 'Room Satisfaction'),
        ('schedule_flexibility', 'Schedule Flexibility'),
    ]
    
    entity_type = models.CharField(max_length=20, choices=ENTITY_TYPES)
    entity_id = models.CharField(max_length=255, db_index=True)
    entity_name = models.CharField(max_length=255)
    
    metric_type = models.CharField(max_length=50, choices=METRIC_TYPES)
    
    # Satisfaction score (0-100)
    satisfaction_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )
    
    # Supporting data
    total_responses = models.PositiveIntegerField(default=0)
    response_rate = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        default=0.0
    )
    
    # Time period
    measurement_period_start = models.DateTimeField()
    measurement_period_end = models.DateTimeField()
    
    # Additional data
    breakdown_data = models.JSONField(default=dict, blank=True)
    comments_summary = models.TextField(blank=True)
    
    class Meta:
        db_table = 'analytics_satisfaction_metric'
        ordering = ['-measurement_period_end']
        indexes = [
            models.Index(fields=['entity_type', 'entity_id']),
            models.Index(fields=['metric_type', 'measurement_period_end']),
        ]

    def __str__(self):
        return f"{self.entity_name} - {self.get_metric_type_display()}: {self.satisfaction_score}%"


class PreferenceTrend(BaseModel):
    """
    Model for tracking preference trends over time.
    """
    PREFERENCE_TYPES = [
        ('time_slot', 'Time Slot Preferences'),
        ('day_of_week', 'Day of Week Preferences'),
        ('course_type', 'Course Type Preferences'),
        ('room_type', 'Room Type Preferences'),
        ('workload', 'Workload Preferences'),
    ]
    
    preference_type = models.CharField(max_length=50, choices=PREFERENCE_TYPES)
    
    # Trend identification
    trend_period_start = models.DateTimeField()
    trend_period_end = models.DateTimeField()
    
    # Aggregation level
    department = models.CharField(max_length=100, blank=True)
    faculty = models.CharField(max_length=100, blank=True)
    user_role = models.CharField(max_length=50, blank=True)  # lecturer, student
    
    # Trend data
    trend_data = models.JSONField(help_text="Detailed trend analysis data")
    
    # Summary statistics
    most_popular_option = models.CharField(max_length=255)
    least_popular_option = models.CharField(max_length=255)
    diversity_index = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="Preference diversity index (0-1, higher = more diverse)"
    )
    
    # Change metrics
    change_rate = models.FloatField(
        help_text="Rate of change compared to previous period (%)"
    )
    stability_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Preference stability score (0-100)"
    )
    
    class Meta:
        db_table = 'analytics_preference_trend'
        ordering = ['-trend_period_end']
        indexes = [
            models.Index(fields=['preference_type', 'trend_period_end']),
            models.Index(fields=['department', 'user_role']),
        ]

    def __str__(self):
        return f"{self.get_preference_type_display()} - {self.trend_period_end.date()}"


class ConflictAnalysis(BaseModel):
    """
    Model for analyzing timetable conflicts.
    """
    CONFLICT_TYPES = [
        ('room_conflict', 'Room Conflicts'),
        ('lecturer_conflict', 'Lecturer Conflicts'),
        ('capacity_conflict', 'Capacity Conflicts'),
        ('preference_conflict', 'Preference Conflicts'),
        ('resource_conflict', 'Resource Conflicts'),
    ]
    
    # Analysis identification
    timetable_id = models.CharField(max_length=255, db_index=True)
    analysis_date = models.DateTimeField(default=timezone.now)
    
    conflict_type = models.CharField(max_length=50, choices=CONFLICT_TYPES)
    
    # Conflict statistics
    total_conflicts = models.PositiveIntegerField(default=0)
    resolved_conflicts = models.PositiveIntegerField(default=0)
    unresolved_conflicts = models.PositiveIntegerField(default=0)
    
    # Severity breakdown
    critical_conflicts = models.PositiveIntegerField(default=0)
    high_conflicts = models.PositiveIntegerField(default=0)
    medium_conflicts = models.PositiveIntegerField(default=0)
    low_conflicts = models.PositiveIntegerField(default=0)
    
    # Impact analysis
    affected_courses = models.PositiveIntegerField(default=0)
    affected_lecturers = models.PositiveIntegerField(default=0)
    affected_students = models.PositiveIntegerField(default=0)
    affected_rooms = models.PositiveIntegerField(default=0)
    
    # Resolution metrics
    average_resolution_time_hours = models.FloatField(null=True, blank=True)
    resolution_success_rate = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        null=True, blank=True
    )
    
    # Detailed analysis
    conflict_patterns = models.JSONField(default=dict, blank=True)
    hotspot_analysis = models.JSONField(default=dict, blank=True)
    recommendations = models.JSONField(default=list, blank=True)
    
    class Meta:
        db_table = 'analytics_conflict_analysis'
        ordering = ['-analysis_date']
        indexes = [
            models.Index(fields=['timetable_id', 'conflict_type']),
            models.Index(fields=['analysis_date']),
        ]

    def __str__(self):
        return f"{self.get_conflict_type_display()} Analysis - {self.analysis_date.date()}"

    @property
    def conflict_resolution_rate(self):
        """Calculate conflict resolution rate."""
        if self.total_conflicts > 0:
            return (self.resolved_conflicts / self.total_conflicts) * 100
        return 0.0


class UsagePattern(BaseModel):
    """
    Model for tracking system usage patterns.
    """
    PATTERN_TYPES = [
        ('login_pattern', 'Login Patterns'),
        ('feature_usage', 'Feature Usage'),
        ('peak_hours', 'Peak Usage Hours'),
        ('seasonal_trends', 'Seasonal Trends'),
        ('user_behavior', 'User Behavior Patterns'),
    ]
    
    pattern_type = models.CharField(max_length=50, choices=PATTERN_TYPES)
    
    # Time period
    analysis_period_start = models.DateTimeField()
    analysis_period_end = models.DateTimeField()
    
    # Pattern data
    pattern_data = models.JSONField(help_text="Detailed pattern analysis data")
    
    # Summary metrics
    peak_usage_time = models.CharField(max_length=100, blank=True)
    average_daily_users = models.FloatField(null=True, blank=True)
    growth_rate = models.FloatField(null=True, blank=True, help_text="Growth rate percentage")
    
    # Insights
    key_insights = models.JSONField(default=list, blank=True)
    recommendations = models.JSONField(default=list, blank=True)
    
    class Meta:
        db_table = 'analytics_usage_pattern'
        ordering = ['-analysis_period_end']
        indexes = [
            models.Index(fields=['pattern_type', 'analysis_period_end']),
        ]

    def __str__(self):
        return f"{self.get_pattern_type_display()} - {self.analysis_period_end.date()}"
