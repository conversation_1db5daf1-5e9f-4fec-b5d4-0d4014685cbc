"""
URLs configuration for analytics app.
"""
from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

app_name = 'analytics'

# Create router for viewsets
router = DefaultRouter()
router.register(r'timetables', views.TimetableAnalyticsViewSet, basename='timetable-analytics')
router.register(r'satisfaction', views.SatisfactionMetricViewSet, basename='satisfaction-metrics')
router.register(r'preferences', views.PreferenceTrendViewSet, basename='preference-trends')
router.register(r'conflicts', views.ConflictAnalysisViewSet, basename='conflict-analysis')

urlpatterns = [
    # ViewSet URLs
    path('', include(router.urls)),
    
    # Additional endpoints
    path('trigger-update/', views.trigger_analytics_update, name='trigger-update'),
    path('status/', views.analytics_status, name='analytics-status'),
]
