"""
Analytics views for the analytics service.
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON>Filter, OrderingFilter
from django.utils import timezone
from datetime import <PERSON>el<PERSON>
from .models import TimetableAnalytics, SatisfactionMetric, PreferenceTrend, ConflictAnalysis, UsagePattern
from .serializers import (
    TimetableAnalyticsSerializer, SatisfactionMetricSerializer,
    PreferenceTrendSerializer, ConflictAnalysisSerializer, UsagePatternSerializer
)
from .services import TimetableAnalyticsService, SatisfactionAnalyticsService, PreferenceTrendService
from .tasks import analyze_timetable_efficiency, calculate_satisfaction_metrics, analyze_preference_trends
import logging

logger = logging.getLogger(__name__)


class TimetableAnalyticsViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for timetable analytics data.
    """
    queryset = TimetableAnalytics.objects.all()
    serializer_class = TimetableAnalyticsSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['academic_year', 'semester', 'department']
    search_fields = ['timetable_name', 'department']
    ordering_fields = ['analysis_date', 'overall_efficiency_score', 'conflict_rate']
    ordering = ['-analysis_date']
    
    @action(detail=False, methods=['get'])
    def efficiency_summary(self, request):
        """Get efficiency summary statistics."""
        queryset = self.filter_queryset(self.get_queryset())
        
        if not queryset.exists():
            return Response({
                'message': 'No analytics data available',
                'summary': {}
            })
        
        # Calculate summary statistics
        from django.db.models import Avg, Max, Min, Count
        
        summary = queryset.aggregate(
            total_analyses=Count('id'),
            avg_efficiency=Avg('overall_efficiency_score'),
            max_efficiency=Max('overall_efficiency_score'),
            min_efficiency=Min('overall_efficiency_score'),
            avg_conflicts=Avg('conflict_rate'),
            avg_room_utilization=Avg('room_utilization_rate'),
            avg_workload_balance=Avg('lecturer_workload_balance_score')
        )
        
        # Round values
        for key, value in summary.items():
            if value is not None and isinstance(value, float):
                summary[key] = round(value, 2)
        
        return Response({
            'summary': summary,
            'period': {
                'start': queryset.earliest('analysis_date').analysis_date,
                'end': queryset.latest('analysis_date').analysis_date
            }
        })
    
    @action(detail=False, methods=['get'])
    def trends(self, request):
        """Get efficiency trends over time."""
        days = int(request.query_params.get('days', 30))
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        queryset = self.get_queryset().filter(
            analysis_date__gte=start_date
        ).order_by('analysis_date')
        
        # Group by date and calculate daily averages
        import pandas as pd
        
        data = list(queryset.values(
            'analysis_date', 'overall_efficiency_score', 'conflict_rate',
            'room_utilization_rate', 'lecturer_workload_balance_score'
        ))
        
        if not data:
            return Response({'trends': [], 'summary': {}})
        
        df = pd.DataFrame(data)
        df['date'] = pd.to_datetime(df['analysis_date']).dt.date
        
        daily_trends = df.groupby('date').agg({
            'overall_efficiency_score': 'mean',
            'conflict_rate': 'mean',
            'room_utilization_rate': 'mean',
            'lecturer_workload_balance_score': 'mean'
        }).reset_index()
        
        trends = []
        for _, row in daily_trends.iterrows():
            trends.append({
                'date': row['date'].isoformat(),
                'efficiency_score': round(row['overall_efficiency_score'], 2),
                'conflict_rate': round(row['conflict_rate'], 2),
                'room_utilization': round(row['room_utilization_rate'], 2),
                'workload_balance': round(row['lecturer_workload_balance_score'], 2)
            })
        
        return Response({
            'trends': trends,
            'period_days': days,
            'total_data_points': len(data)
        })
    
    @action(detail=True, methods=['post'])
    def reanalyze(self, request, pk=None):
        """Trigger re-analysis of a specific timetable."""
        analytics = self.get_object()
        
        # Queue analysis task
        task = analyze_timetable_efficiency.delay(analytics.timetable_id)
        
        return Response({
            'message': 'Re-analysis queued',
            'task_id': task.id,
            'timetable_id': analytics.timetable_id
        }, status=status.HTTP_202_ACCEPTED)


class SatisfactionMetricViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for satisfaction metrics.
    """
    queryset = SatisfactionMetric.objects.all()
    serializer_class = SatisfactionMetricSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['entity_type', 'metric_type']
    search_fields = ['entity_name']
    ordering_fields = ['measurement_period_end', 'satisfaction_score']
    ordering = ['-measurement_period_end']
    
    @action(detail=False, methods=['get'])
    def by_entity_type(self, request):
        """Get satisfaction metrics grouped by entity type."""
        from django.db.models import Avg, Count
        
        entity_type = request.query_params.get('entity_type')
        queryset = self.get_queryset()
        
        if entity_type:
            queryset = queryset.filter(entity_type=entity_type)
        
        # Group by entity type and metric type
        results = queryset.values('entity_type', 'metric_type').annotate(
            avg_satisfaction=Avg('satisfaction_score'),
            count=Count('id')
        ).order_by('entity_type', 'metric_type')
        
        # Organize by entity type
        grouped_results = {}
        for item in results:
            entity_type = item['entity_type']
            if entity_type not in grouped_results:
                grouped_results[entity_type] = {}
            
            grouped_results[entity_type][item['metric_type']] = {
                'average_satisfaction': round(item['avg_satisfaction'], 2),
                'count': item['count']
            }
        
        return Response(grouped_results)
    
    @action(detail=False, methods=['get'])
    def trends(self, request):
        """Get satisfaction trends over time."""
        entity_type = request.query_params.get('entity_type')
        metric_type = request.query_params.get('metric_type')
        days = int(request.query_params.get('days', 90))
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        queryset = self.get_queryset().filter(
            measurement_period_end__gte=start_date
        )
        
        if entity_type:
            queryset = queryset.filter(entity_type=entity_type)
        if metric_type:
            queryset = queryset.filter(metric_type=metric_type)
        
        # Group by week and calculate averages
        import pandas as pd
        
        data = list(queryset.values(
            'measurement_period_end', 'satisfaction_score', 'entity_type', 'metric_type'
        ))
        
        if not data:
            return Response({'trends': []})
        
        df = pd.DataFrame(data)
        df['week'] = pd.to_datetime(df['measurement_period_end']).dt.to_period('W')
        
        weekly_trends = df.groupby(['week', 'entity_type', 'metric_type']).agg({
            'satisfaction_score': 'mean'
        }).reset_index()
        
        trends = []
        for _, row in weekly_trends.iterrows():
            trends.append({
                'week': str(row['week']),
                'entity_type': row['entity_type'],
                'metric_type': row['metric_type'],
                'satisfaction_score': round(row['satisfaction_score'], 2)
            })
        
        return Response({'trends': trends})


class PreferenceTrendViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for preference trends.
    """
    queryset = PreferenceTrend.objects.all()
    serializer_class = PreferenceTrendSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['preference_type', 'user_role', 'department']
    ordering_fields = ['trend_period_end', 'diversity_index', 'stability_score']
    ordering = ['-trend_period_end']
    
    @action(detail=False, methods=['get'])
    def diversity_analysis(self, request):
        """Get preference diversity analysis."""
        from django.db.models import Avg
        
        # Calculate average diversity by preference type and user role
        diversity_data = self.get_queryset().values(
            'preference_type', 'user_role'
        ).annotate(
            avg_diversity=Avg('diversity_index'),
            avg_stability=Avg('stability_score'),
            count=Count('id')
        ).order_by('preference_type', 'user_role')
        
        # Format results
        results = []
        for item in diversity_data:
            results.append({
                'preference_type': item['preference_type'],
                'user_role': item['user_role'],
                'average_diversity': round(item['avg_diversity'], 3),
                'average_stability': round(item['avg_stability'], 2),
                'trend_count': item['count']
            })
        
        return Response(results)
    
    @action(detail=False, methods=['get'])
    def popular_preferences(self, request):
        """Get most popular preferences across different categories."""
        preference_type = request.query_params.get('preference_type')
        user_role = request.query_params.get('user_role')
        
        queryset = self.get_queryset()
        
        if preference_type:
            queryset = queryset.filter(preference_type=preference_type)
        if user_role:
            queryset = queryset.filter(user_role=user_role)
        
        # Get most popular options
        popular_options = {}
        for trend in queryset:
            key = f"{trend.preference_type}_{trend.user_role}"
            if key not in popular_options:
                popular_options[key] = []
            popular_options[key].append(trend.most_popular_option)
        
        # Count occurrences
        results = []
        for key, options in popular_options.items():
            preference_type, user_role = key.split('_', 1)
            option_counts = {}
            for option in options:
                option_counts[option] = option_counts.get(option, 0) + 1
            
            # Sort by count
            sorted_options = sorted(option_counts.items(), key=lambda x: x[1], reverse=True)
            
            results.append({
                'preference_type': preference_type,
                'user_role': user_role,
                'popular_options': [
                    {'option': option, 'count': count}
                    for option, count in sorted_options[:5]  # Top 5
                ]
            })
        
        return Response(results)


class ConflictAnalysisViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for conflict analysis data.
    """
    queryset = ConflictAnalysis.objects.all()
    serializer_class = ConflictAnalysisSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['conflict_type', 'timetable_id']
    ordering_fields = ['analysis_date', 'total_conflicts']
    ordering = ['-analysis_date']
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get conflict analysis summary."""
        from django.db.models import Sum, Avg, Count
        
        queryset = self.filter_queryset(self.get_queryset())
        
        summary = queryset.aggregate(
            total_analyses=Count('id'),
            total_conflicts=Sum('total_conflicts'),
            avg_conflicts_per_analysis=Avg('total_conflicts'),
            total_resolved=Sum('resolved_conflicts'),
            total_unresolved=Sum('unresolved_conflicts')
        )
        
        # Calculate resolution rate
        if summary['total_conflicts'] and summary['total_conflicts'] > 0:
            summary['resolution_rate'] = round(
                (summary['total_resolved'] / summary['total_conflicts']) * 100, 2
            )
        else:
            summary['resolution_rate'] = 0.0
        
        # Round values
        for key, value in summary.items():
            if value is not None and isinstance(value, float) and key != 'resolution_rate':
                summary[key] = round(value, 2)
        
        return Response(summary)
    
    @action(detail=False, methods=['get'])
    def hotspots(self, request):
        """Get conflict hotspots analysis."""
        days = int(request.query_params.get('days', 30))
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        queryset = self.get_queryset().filter(
            analysis_date__gte=start_date
        )
        
        # Analyze hotspots from all conflict analyses
        all_hotspots = {
            'rooms': {},
            'lecturers': {},
            'time_slots': {}
        }
        
        for analysis in queryset:
            hotspots = analysis.hotspot_analysis
            for category, items in hotspots.items():
                if category in all_hotspots:
                    for item in items:
                        item_id = item.get('id', item.get('name', 'unknown'))
                        if item_id not in all_hotspots[category]:
                            all_hotspots[category][item_id] = 0
                        all_hotspots[category][item_id] += item.get('conflict_count', 1)
        
        # Sort hotspots by conflict count
        sorted_hotspots = {}
        for category, items in all_hotspots.items():
            sorted_items = sorted(items.items(), key=lambda x: x[1], reverse=True)
            sorted_hotspots[category] = [
                {'id': item_id, 'conflict_count': count}
                for item_id, count in sorted_items[:10]  # Top 10
            ]
        
        return Response({
            'hotspots': sorted_hotspots,
            'period_days': days,
            'analyses_included': queryset.count()
        })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def trigger_analytics_update(request):
    """
    Trigger analytics data update.
    """
    try:
        # Queue analytics tasks
        from .tasks import update_analytics_data, calculate_satisfaction_metrics, analyze_preference_trends
        
        tasks = []
        
        # Update analytics data
        task1 = update_analytics_data.delay()
        tasks.append({'task': 'update_analytics_data', 'task_id': task1.id})
        
        # Calculate satisfaction metrics
        task2 = calculate_satisfaction_metrics.delay()
        tasks.append({'task': 'calculate_satisfaction_metrics', 'task_id': task2.id})
        
        # Analyze preference trends
        task3 = analyze_preference_trends.delay()
        tasks.append({'task': 'analyze_preference_trends', 'task_id': task3.id})
        
        return Response({
            'message': 'Analytics update triggered',
            'tasks': tasks
        }, status=status.HTTP_202_ACCEPTED)
        
    except Exception as e:
        logger.error(f"Failed to trigger analytics update: {e}")
        return Response({
            'error': 'Failed to trigger analytics update',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def analytics_status(request):
    """
    Get analytics processing status.
    """
    try:
        from celery import current_app
        
        # Get active tasks
        inspect = current_app.control.inspect()
        active_tasks = inspect.active()
        
        # Filter analytics-related tasks
        analytics_tasks = []
        if active_tasks:
            for worker, tasks in active_tasks.items():
                for task in tasks:
                    if 'analytics' in task['name'].lower():
                        analytics_tasks.append({
                            'worker': worker,
                            'task_name': task['name'],
                            'task_id': task['id'],
                            'args': task['args']
                        })
        
        # Get recent analytics data counts
        from django.db.models import Count
        
        recent_counts = {
            'timetable_analyses': TimetableAnalytics.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).count(),
            'satisfaction_metrics': SatisfactionMetric.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).count(),
            'preference_trends': PreferenceTrend.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).count(),
            'conflict_analyses': ConflictAnalysis.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).count()
        }
        
        return Response({
            'active_tasks': analytics_tasks,
            'recent_data_counts': recent_counts,
            'last_updated': timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Failed to get analytics status: {e}")
        return Response({
            'error': 'Failed to get analytics status',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
