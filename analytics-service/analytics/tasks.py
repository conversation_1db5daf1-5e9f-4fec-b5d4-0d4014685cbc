"""
Celery tasks for analytics processing.
"""
from celery import shared_task
from django.utils import timezone
from django.conf import settings
from datetime import timedelta
from .models import TimetableAnalytics, SatisfactionMetric, PreferenceTrend, ConflictAnalysis
from .services import (
    TimetableAnalyticsService, SatisfactionAnalyticsService,
    PreferenceTrendService, ConflictAnalyticsService, DataCollectionService
)
from core.models import AnalyticsMetric
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def update_analytics_data(self):
    """
    Update analytics data from all services.
    """
    try:
        data_collector = DataCollectionService()
        
        # Collect recent timetables
        end_date = timezone.now()
        start_date = end_date - timedelta(hours=1)  # Last hour
        
        timetable_data = data_collector.collect_timetable_data(
            date_range={'start': start_date, 'end': end_date}
        )
        
        if timetable_data:
            timetables = timetable_data.get('results', [])
            
            for timetable in timetables:
                # Queue individual timetable analysis
                analyze_timetable_efficiency.delay(timetable['id'])
        
        logger.info(f"Queued analysis for {len(timetables) if timetable_data else 0} timetables")
        
        return {
            'status': 'success',
            'timetables_queued': len(timetables) if timetable_data else 0
        }
        
    except Exception as e:
        logger.error(f"Failed to update analytics data: {e}")
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'status': 'failed', 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def analyze_timetable_efficiency(self, timetable_id):
    """
    Analyze efficiency for a specific timetable.
    """
    try:
        analytics_service = TimetableAnalyticsService()
        
        # Perform efficiency analysis
        efficiency_metrics = analytics_service.analyze_timetable_efficiency(timetable_id)
        
        if not efficiency_metrics:
            logger.warning(f"No efficiency metrics calculated for timetable {timetable_id}")
            return {'status': 'no_data', 'timetable_id': timetable_id}
        
        # Get timetable details
        data_collector = DataCollectionService()
        timetable_data = data_collector.collect_timetable_data(timetable_id)
        
        if not timetable_data:
            logger.warning(f"Could not fetch timetable data for {timetable_id}")
            return {'status': 'no_timetable_data', 'timetable_id': timetable_id}
        
        # Create or update analytics record
        now = timezone.now()
        analytics, created = TimetableAnalytics.objects.update_or_create(
            timetable_id=timetable_id,
            analysis_date__date=now.date(),
            defaults={
                'timetable_name': timetable_data.get('name', ''),
                'academic_year': timetable_data.get('academic_year', ''),
                'semester': timetable_data.get('semester', ''),
                'department': timetable_data.get('department', ''),
                'analysis_date': now,
                'analysis_period_start': now - timedelta(hours=24),
                'analysis_period_end': now,
                'overall_efficiency_score': efficiency_metrics['overall_efficiency_score'],
                'conflict_rate': efficiency_metrics['conflict_rate'],
                'room_utilization_rate': efficiency_metrics['room_utilization_rate'],
                'time_slot_utilization': efficiency_metrics['time_slot_utilization'],
                'lecturer_workload_balance_score': efficiency_metrics['lecturer_workload_balance_score'],
                'detailed_metrics': efficiency_metrics
            }
        )
        
        # Create individual metrics
        for metric_type, value in efficiency_metrics.items():
            AnalyticsMetric.objects.create(
                metric_type=metric_type,
                aggregation_level='timetable',
                entity_id=timetable_id,
                entity_name=timetable_data.get('name', ''),
                value=value,
                period_start=now - timedelta(hours=24),
                period_end=now,
                metadata={'timetable_data': timetable_data}
            )
        
        logger.info(f"Analyzed timetable efficiency for {timetable_id}: {efficiency_metrics['overall_efficiency_score']:.2f}%")
        
        return {
            'status': 'success',
            'timetable_id': timetable_id,
            'efficiency_score': efficiency_metrics['overall_efficiency_score'],
            'created': created
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze timetable efficiency for {timetable_id}: {e}")
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'status': 'failed', 'timetable_id': timetable_id, 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def calculate_satisfaction_metrics(self):
    """
    Calculate satisfaction metrics for all entities.
    """
    try:
        satisfaction_service = SatisfactionAnalyticsService()
        data_collector = DataCollectionService()
        
        # Calculate satisfaction for different entity types
        entity_types = ['lecturer', 'student', 'department', 'system']
        results = []
        
        for entity_type in entity_types:
            try:
                # Get entities of this type
                if entity_type == 'lecturer':
                    entities = data_collector.collect_user_data(role='lecturer')
                elif entity_type == 'student':
                    entities = data_collector.collect_user_data(role='student')
                elif entity_type == 'department':
                    # Get unique departments
                    user_data = data_collector.collect_user_data()
                    if user_data:
                        departments = list(set([u.get('department') for u in user_data.get('results', []) if u.get('department')]))
                        entities = {'results': [{'id': d, 'name': d} for d in departments]}
                    else:
                        entities = None
                else:  # system
                    entities = {'results': [{'id': 'system', 'name': 'System-wide'}]}
                
                if not entities:
                    continue
                
                # Calculate satisfaction for each entity
                for entity in entities.get('results', []):
                    entity_id = entity['id']
                    entity_name = entity.get('name', entity_id)
                    
                    # Calculate satisfaction for last 30 days
                    end_date = timezone.now()
                    start_date = end_date - timedelta(days=30)
                    
                    satisfaction_score = satisfaction_service.calculate_preference_satisfaction(
                        entity_type, entity_id, start_date, end_date
                    )
                    
                    # Create satisfaction metric record
                    SatisfactionMetric.objects.create(
                        entity_type=entity_type,
                        entity_id=str(entity_id),
                        entity_name=entity_name,
                        metric_type='overall_satisfaction',
                        satisfaction_score=satisfaction_score,
                        measurement_period_start=start_date,
                        measurement_period_end=end_date,
                        total_responses=1,  # Would be calculated from actual survey data
                        response_rate=100.0  # Would be calculated from actual survey data
                    )
                    
                    results.append({
                        'entity_type': entity_type,
                        'entity_id': entity_id,
                        'satisfaction_score': satisfaction_score
                    })
                    
            except Exception as e:
                logger.error(f"Failed to calculate satisfaction for {entity_type}: {e}")
                continue
        
        logger.info(f"Calculated satisfaction metrics for {len(results)} entities")
        
        return {
            'status': 'success',
            'metrics_calculated': len(results),
            'results': results
        }
        
    except Exception as e:
        logger.error(f"Failed to calculate satisfaction metrics: {e}")
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'status': 'failed', 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def analyze_preference_trends(self):
    """
    Analyze preference trends across the system.
    """
    try:
        trend_service = PreferenceTrendService()
        
        # Analyze trends for different user roles and preference types
        preference_types = ['time_slot', 'day_of_week', 'course_type']
        user_roles = ['lecturer', 'student']
        
        results = []
        
        for preference_type in preference_types:
            for user_role in user_roles:
                try:
                    # Analyze trends for last 90 days
                    trend_data = trend_service.analyze_time_preference_trends(
                        user_role=user_role,
                        period_days=90
                    )
                    
                    if trend_data:
                        # Create preference trend record
                        now = timezone.now()
                        PreferenceTrend.objects.create(
                            preference_type=preference_type,
                            trend_period_start=now - timedelta(days=90),
                            trend_period_end=now,
                            user_role=user_role,
                            trend_data=trend_data['trend_data'],
                            most_popular_option=trend_data['most_popular_option'],
                            least_popular_option=trend_data['least_popular_option'],
                            diversity_index=trend_data['diversity_index'],
                            change_rate=0.0,  # Would be calculated by comparing with previous period
                            stability_score=75.0  # Would be calculated based on trend consistency
                        )
                        
                        results.append({
                            'preference_type': preference_type,
                            'user_role': user_role,
                            'diversity_index': trend_data['diversity_index'],
                            'most_popular': trend_data['most_popular_option']
                        })
                        
                except Exception as e:
                    logger.error(f"Failed to analyze {preference_type} trends for {user_role}: {e}")
                    continue
        
        logger.info(f"Analyzed preference trends for {len(results)} combinations")
        
        return {
            'status': 'success',
            'trends_analyzed': len(results),
            'results': results
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze preference trends: {e}")
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'status': 'failed', 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def analyze_conflicts(self, timetable_id):
    """
    Analyze conflicts for a specific timetable.
    """
    try:
        conflict_service = ConflictAnalyticsService()
        
        # Analyze conflict patterns
        conflict_analysis = conflict_service.analyze_conflict_patterns(timetable_id)
        
        if not conflict_analysis:
            logger.warning(f"No conflict analysis data for timetable {timetable_id}")
            return {'status': 'no_data', 'timetable_id': timetable_id}
        
        # Create conflict analysis record
        now = timezone.now()
        ConflictAnalysis.objects.create(
            timetable_id=timetable_id,
            analysis_date=now,
            conflict_type='all_conflicts',  # Summary of all conflict types
            total_conflicts=conflict_analysis['total_conflicts'],
            conflict_patterns=conflict_analysis['conflicts_by_type'],
            hotspot_analysis=conflict_analysis['hotspots'],
            recommendations=conflict_analysis['recommendations']
        )
        
        logger.info(f"Analyzed conflicts for timetable {timetable_id}: {conflict_analysis['total_conflicts']} conflicts found")
        
        return {
            'status': 'success',
            'timetable_id': timetable_id,
            'total_conflicts': conflict_analysis['total_conflicts']
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze conflicts for timetable {timetable_id}: {e}")
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'status': 'failed', 'timetable_id': timetable_id, 'error': str(e)}


@shared_task
def cleanup_old_analytics_data():
    """
    Clean up old analytics data based on retention policy.
    """
    try:
        cutoff_date = timezone.now() - timedelta(days=settings.ANALYTICS_DATA_RETENTION_DAYS)
        
        # Delete old records
        deleted_analytics = TimetableAnalytics.objects.filter(created_at__lt=cutoff_date).delete()
        deleted_satisfaction = SatisfactionMetric.objects.filter(created_at__lt=cutoff_date).delete()
        deleted_trends = PreferenceTrend.objects.filter(created_at__lt=cutoff_date).delete()
        deleted_conflicts = ConflictAnalysis.objects.filter(created_at__lt=cutoff_date).delete()
        deleted_metrics = AnalyticsMetric.objects.filter(created_at__lt=cutoff_date).delete()
        
        logger.info(f"Cleaned up old analytics data: {deleted_analytics[0]} analytics, {deleted_satisfaction[0]} satisfaction metrics, {deleted_trends[0]} trends, {deleted_conflicts[0]} conflict analyses, {deleted_metrics[0]} metrics")
        
        return {
            'status': 'success',
            'deleted_analytics': deleted_analytics[0],
            'deleted_satisfaction': deleted_satisfaction[0],
            'deleted_trends': deleted_trends[0],
            'deleted_conflicts': deleted_conflicts[0],
            'deleted_metrics': deleted_metrics[0]
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup old analytics data: {e}")
        return {'status': 'failed', 'error': str(e)}
