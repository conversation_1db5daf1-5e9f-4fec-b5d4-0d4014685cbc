"""
Serializers for analytics models.
"""
from rest_framework import serializers
from .models import TimetableAnalytics, SatisfactionMetric, PreferenceTrend, ConflictAnalysis, UsagePattern


class TimetableAnalyticsSerializer(serializers.ModelSerializer):
    """
    Serializer for TimetableAnalytics model.
    """
    efficiency_grade = serializers.SerializerMethodField()
    conflict_level = serializers.SerializerMethodField()
    
    class Meta:
        model = TimetableAnalytics
        fields = [
            'id', 'timetable_id', 'timetable_name', 'academic_year', 'semester',
            'department', 'analysis_date', 'analysis_period_start', 'analysis_period_end',
            'overall_efficiency_score', 'efficiency_grade', 'conflict_count', 'conflict_rate',
            'conflict_level', 'lecturer_preference_satisfaction', 'student_preference_satisfaction',
            'room_utilization_rate', 'time_slot_utilization', 'lecturer_workload_balance_score',
            'total_courses', 'total_lecturers', 'total_students', 'total_rooms',
            'detailed_metrics', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_efficiency_grade(self, obj):
        """Calculate efficiency grade based on score."""
        score = obj.overall_efficiency_score
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'
    
    def get_conflict_level(self, obj):
        """Determine conflict level based on conflict rate."""
        rate = obj.conflict_rate
        if rate <= 5:
            return 'Low'
        elif rate <= 15:
            return 'Medium'
        elif rate <= 30:
            return 'High'
        else:
            return 'Critical'


class SatisfactionMetricSerializer(serializers.ModelSerializer):
    """
    Serializer for SatisfactionMetric model.
    """
    entity_type_display = serializers.CharField(source='get_entity_type_display', read_only=True)
    metric_type_display = serializers.CharField(source='get_metric_type_display', read_only=True)
    satisfaction_level = serializers.SerializerMethodField()
    
    class Meta:
        model = SatisfactionMetric
        fields = [
            'id', 'entity_type', 'entity_type_display', 'entity_id', 'entity_name',
            'metric_type', 'metric_type_display', 'satisfaction_score', 'satisfaction_level',
            'total_responses', 'response_rate', 'measurement_period_start',
            'measurement_period_end', 'breakdown_data', 'comments_summary',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_satisfaction_level(self, obj):
        """Determine satisfaction level based on score."""
        score = obj.satisfaction_score
        if score >= 80:
            return 'High'
        elif score >= 60:
            return 'Medium'
        elif score >= 40:
            return 'Low'
        else:
            return 'Very Low'


class PreferenceTrendSerializer(serializers.ModelSerializer):
    """
    Serializer for PreferenceTrend model.
    """
    preference_type_display = serializers.CharField(source='get_preference_type_display', read_only=True)
    diversity_level = serializers.SerializerMethodField()
    stability_level = serializers.SerializerMethodField()
    trend_direction = serializers.SerializerMethodField()
    
    class Meta:
        model = PreferenceTrend
        fields = [
            'id', 'preference_type', 'preference_type_display', 'trend_period_start',
            'trend_period_end', 'department', 'faculty', 'user_role', 'trend_data',
            'most_popular_option', 'least_popular_option', 'diversity_index',
            'diversity_level', 'change_rate', 'stability_score', 'stability_level',
            'trend_direction', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_diversity_level(self, obj):
        """Determine diversity level based on diversity index."""
        index = obj.diversity_index
        if index >= 0.8:
            return 'Very High'
        elif index >= 0.6:
            return 'High'
        elif index >= 0.4:
            return 'Medium'
        elif index >= 0.2:
            return 'Low'
        else:
            return 'Very Low'
    
    def get_stability_level(self, obj):
        """Determine stability level based on stability score."""
        score = obj.stability_score
        if score >= 80:
            return 'Very Stable'
        elif score >= 60:
            return 'Stable'
        elif score >= 40:
            return 'Moderate'
        elif score >= 20:
            return 'Unstable'
        else:
            return 'Very Unstable'
    
    def get_trend_direction(self, obj):
        """Determine trend direction based on change rate."""
        rate = obj.change_rate
        if rate > 10:
            return 'Increasing'
        elif rate < -10:
            return 'Decreasing'
        else:
            return 'Stable'


class ConflictAnalysisSerializer(serializers.ModelSerializer):
    """
    Serializer for ConflictAnalysis model.
    """
    conflict_type_display = serializers.CharField(source='get_conflict_type_display', read_only=True)
    resolution_rate = serializers.ReadOnlyField()
    severity_breakdown = serializers.SerializerMethodField()
    impact_summary = serializers.SerializerMethodField()
    
    class Meta:
        model = ConflictAnalysis
        fields = [
            'id', 'timetable_id', 'analysis_date', 'conflict_type', 'conflict_type_display',
            'total_conflicts', 'resolved_conflicts', 'unresolved_conflicts', 'resolution_rate',
            'critical_conflicts', 'high_conflicts', 'medium_conflicts', 'low_conflicts',
            'severity_breakdown', 'affected_courses', 'affected_lecturers', 'affected_students',
            'affected_rooms', 'impact_summary', 'average_resolution_time_hours',
            'resolution_success_rate', 'conflict_patterns', 'hotspot_analysis',
            'recommendations', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'resolution_rate', 'created_at', 'updated_at']
    
    def get_severity_breakdown(self, obj):
        """Get conflict severity breakdown as percentages."""
        total = obj.total_conflicts
        if total == 0:
            return {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        
        return {
            'critical': round((obj.critical_conflicts / total) * 100, 1),
            'high': round((obj.high_conflicts / total) * 100, 1),
            'medium': round((obj.medium_conflicts / total) * 100, 1),
            'low': round((obj.low_conflicts / total) * 100, 1)
        }
    
    def get_impact_summary(self, obj):
        """Get impact summary."""
        return {
            'total_affected_entities': (
                obj.affected_courses + obj.affected_lecturers + 
                obj.affected_students + obj.affected_rooms
            ),
            'courses_affected': obj.affected_courses,
            'lecturers_affected': obj.affected_lecturers,
            'students_affected': obj.affected_students,
            'rooms_affected': obj.affected_rooms
        }


class UsagePatternSerializer(serializers.ModelSerializer):
    """
    Serializer for UsagePattern model.
    """
    pattern_type_display = serializers.CharField(source='get_pattern_type_display', read_only=True)
    growth_trend = serializers.SerializerMethodField()
    
    class Meta:
        model = UsagePattern
        fields = [
            'id', 'pattern_type', 'pattern_type_display', 'analysis_period_start',
            'analysis_period_end', 'pattern_data', 'peak_usage_time',
            'average_daily_users', 'growth_rate', 'growth_trend',
            'key_insights', 'recommendations', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_growth_trend(self, obj):
        """Determine growth trend based on growth rate."""
        if obj.growth_rate is None:
            return 'Unknown'
        
        rate = obj.growth_rate
        if rate > 20:
            return 'Rapid Growth'
        elif rate > 5:
            return 'Growing'
        elif rate > -5:
            return 'Stable'
        elif rate > -20:
            return 'Declining'
        else:
            return 'Rapid Decline'


class AnalyticsSummarySerializer(serializers.Serializer):
    """
    Serializer for analytics summary data.
    """
    total_analyses = serializers.IntegerField()
    avg_efficiency = serializers.FloatField()
    max_efficiency = serializers.FloatField()
    min_efficiency = serializers.FloatField()
    avg_conflicts = serializers.FloatField()
    avg_room_utilization = serializers.FloatField()
    avg_workload_balance = serializers.FloatField()
    period_start = serializers.DateTimeField()
    period_end = serializers.DateTimeField()


class TrendDataSerializer(serializers.Serializer):
    """
    Serializer for trend data.
    """
    date = serializers.DateField()
    efficiency_score = serializers.FloatField()
    conflict_rate = serializers.FloatField()
    room_utilization = serializers.FloatField()
    workload_balance = serializers.FloatField()


class ConflictHotspotSerializer(serializers.Serializer):
    """
    Serializer for conflict hotspot data.
    """
    id = serializers.CharField()
    conflict_count = serializers.IntegerField()


class ConflictHotspotsSerializer(serializers.Serializer):
    """
    Serializer for conflict hotspots response.
    """
    rooms = ConflictHotspotSerializer(many=True)
    lecturers = ConflictHotspotSerializer(many=True)
    time_slots = ConflictHotspotSerializer(many=True)
    period_days = serializers.IntegerField()
    analyses_included = serializers.IntegerField()


class PopularPreferenceSerializer(serializers.Serializer):
    """
    Serializer for popular preference data.
    """
    option = serializers.CharField()
    count = serializers.IntegerField()


class PreferencePopularitySerializer(serializers.Serializer):
    """
    Serializer for preference popularity response.
    """
    preference_type = serializers.CharField()
    user_role = serializers.CharField()
    popular_options = PopularPreferenceSerializer(many=True)


class DiversityAnalysisSerializer(serializers.Serializer):
    """
    Serializer for diversity analysis data.
    """
    preference_type = serializers.CharField()
    user_role = serializers.CharField()
    average_diversity = serializers.FloatField()
    average_stability = serializers.FloatField()
    trend_count = serializers.IntegerField()
