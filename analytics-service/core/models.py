"""
Core models for the analytics service.
"""
from django.db import models
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.core.validators import EmailValidator
from django.utils import timezone
import uuid


class BaseModel(models.Model):
    """
    Abstract base model with common fields for all models.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True


class User(AbstractBaseUser, PermissionsMixin, BaseModel):
    """
    User model proxy for analytics service (synced from user management service).
    """
    email = models.EmailField(
        unique=True,
        validators=[EmailValidator()],
        help_text="User's email address"
    )
    username = models.Char<PERSON><PERSON>(
        max_length=150,
        unique=True,
        help_text="Unique username for the user"
    )
    first_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150, blank=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    date_joined = models.DateTimeField(default=timezone.now)
    last_login = models.DateTimeField(blank=True, null=True)
    
    # Role information
    role = models.CharField(
        max_length=50,
        choices=[
            ('superadmin', 'Super Admin'),
            ('admin', 'Admin'),
            ('lecturer', 'Lecturer'),
            ('student', 'Student'),
        ],
        default='student'
    )
    
    # Additional information for analytics
    department = models.CharField(max_length=100, blank=True)
    faculty = models.CharField(max_length=100, blank=True)
    academic_year = models.CharField(max_length=20, blank=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        db_table = 'core_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return f"{self.email} ({self.role})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()


class DataSource(BaseModel):
    """
    Model for tracking data sources and their sync status.
    """
    SOURCE_TYPES = [
        ('user_service', 'User Management Service'),
        ('preference_service', 'Preference Collection Service'),
        ('timetable_service', 'Timetable Generation Service'),
        ('notification_service', 'Notification Service'),
        ('external_api', 'External API'),
        ('manual_upload', 'Manual Upload'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    source_type = models.CharField(max_length=50, choices=SOURCE_TYPES)
    base_url = models.URLField(blank=True)
    api_key = models.CharField(max_length=255, blank=True)
    
    # Sync configuration
    is_enabled = models.BooleanField(default=True)
    sync_interval_minutes = models.PositiveIntegerField(default=60)
    last_sync_at = models.DateTimeField(null=True, blank=True)
    next_sync_at = models.DateTimeField(null=True, blank=True)
    
    # Status tracking
    sync_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('syncing', 'Syncing'),
            ('success', 'Success'),
            ('failed', 'Failed'),
        ],
        default='pending'
    )
    
    last_error = models.TextField(blank=True)
    records_synced = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'core_data_source'
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_source_type_display()})"


class AnalyticsMetric(BaseModel):
    """
    Model for storing calculated analytics metrics.
    """
    METRIC_TYPES = [
        ('satisfaction', 'Satisfaction Score'),
        ('efficiency', 'Efficiency Score'),
        ('utilization', 'Utilization Rate'),
        ('conflict_rate', 'Conflict Rate'),
        ('preference_match', 'Preference Match Rate'),
        ('workload_balance', 'Workload Balance Score'),
        ('room_utilization', 'Room Utilization Rate'),
        ('time_distribution', 'Time Distribution Score'),
    ]
    
    AGGREGATION_LEVELS = [
        ('system', 'System-wide'),
        ('department', 'Department'),
        ('faculty', 'Faculty'),
        ('course', 'Course'),
        ('lecturer', 'Lecturer'),
        ('student', 'Student'),
        ('room', 'Room'),
        ('time_slot', 'Time Slot'),
    ]
    
    metric_type = models.CharField(max_length=50, choices=METRIC_TYPES)
    aggregation_level = models.CharField(max_length=20, choices=AGGREGATION_LEVELS)
    
    # Metric identification
    entity_id = models.CharField(max_length=255, blank=True, help_text="ID of the entity being measured")
    entity_name = models.CharField(max_length=255, blank=True, help_text="Name of the entity being measured")
    
    # Metric values
    value = models.FloatField()
    previous_value = models.FloatField(null=True, blank=True)
    target_value = models.FloatField(null=True, blank=True)
    
    # Time period
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    
    # Additional data
    metadata = models.JSONField(default=dict, blank=True)
    calculation_details = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'core_analytics_metric'
        ordering = ['-period_end', 'metric_type']
        indexes = [
            models.Index(fields=['metric_type', 'aggregation_level']),
            models.Index(fields=['entity_id', 'period_end']),
            models.Index(fields=['period_start', 'period_end']),
        ]

    def __str__(self):
        return f"{self.get_metric_type_display()} - {self.entity_name or self.entity_id} ({self.value})"

    @property
    def change_percentage(self):
        """Calculate percentage change from previous value."""
        if self.previous_value and self.previous_value != 0:
            return ((self.value - self.previous_value) / self.previous_value) * 100
        return None

    @property
    def target_achievement(self):
        """Calculate percentage of target achieved."""
        if self.target_value and self.target_value != 0:
            return (self.value / self.target_value) * 100
        return None


class CacheEntry(BaseModel):
    """
    Model for caching computed analytics results.
    """
    cache_key = models.CharField(max_length=255, unique=True)
    cache_data = models.JSONField()
    expires_at = models.DateTimeField()
    
    # Metadata
    computation_time_seconds = models.FloatField(null=True, blank=True)
    data_size_bytes = models.PositiveIntegerField(null=True, blank=True)
    
    class Meta:
        db_table = 'core_cache_entry'
        ordering = ['-created_at']

    def __str__(self):
        return f"Cache: {self.cache_key}"

    @property
    def is_expired(self):
        """Check if cache entry has expired."""
        return timezone.now() > self.expires_at

    def refresh_expiry(self, hours=1):
        """Refresh the expiry time."""
        self.expires_at = timezone.now() + timezone.timedelta(hours=hours)
        self.save(update_fields=['expires_at'])
