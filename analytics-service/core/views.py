"""
Core views for health checks and basic functionality.
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from django.db import connection
from django.utils import timezone
from django.core.cache import cache
import redis
import pandas as pd
import numpy as np
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """
    Health check endpoint for monitoring service status.
    """
    health_status = {
        'status': 'healthy',
        'service': 'analytics-service',
        'version': '1.0.0',
        'timestamp': timezone.now().isoformat(),
        'checks': {}
    }
    
    # Check database connection
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        health_status['checks']['database'] = 'healthy'
    except Exception as e:
        health_status['checks']['database'] = f'unhealthy: {str(e)}'
        health_status['status'] = 'unhealthy'
        logger.error(f"Database health check failed: {e}")
    
    # Check Redis connection
    try:
        r = redis.from_url(settings.REDIS_URL)
        r.ping()
        health_status['checks']['redis'] = 'healthy'
    except Exception as e:
        health_status['checks']['redis'] = f'unhealthy: {str(e)}'
        health_status['status'] = 'unhealthy'
        logger.error(f"Redis health check failed: {e}")
    
    # Check Celery
    try:
        from celery import current_app
        inspect = current_app.control.inspect()
        stats = inspect.stats()
        if stats:
            health_status['checks']['celery'] = 'healthy'
        else:
            health_status['checks']['celery'] = 'no workers available'
    except Exception as e:
        health_status['checks']['celery'] = f'unhealthy: {str(e)}'
        logger.error(f"Celery health check failed: {e}")
    
    # Check Pandas/NumPy
    try:
        # Simple test to ensure data processing libraries work
        test_data = pd.DataFrame({'test': [1, 2, 3]})
        test_result = np.mean(test_data['test'])
        if test_result == 2.0:
            health_status['checks']['data_processing'] = 'healthy'
        else:
            health_status['checks']['data_processing'] = 'calculation error'
    except Exception as e:
        health_status['checks']['data_processing'] = f'unhealthy: {str(e)}'
        logger.error(f"Data processing health check failed: {e}")
    
    # Check cache
    try:
        cache.set('health_check', 'test', 60)
        cache_value = cache.get('health_check')
        if cache_value == 'test':
            health_status['checks']['cache'] = 'healthy'
        else:
            health_status['checks']['cache'] = 'cache read/write failed'
    except Exception as e:
        health_status['checks']['cache'] = f'unhealthy: {str(e)}'
        logger.error(f"Cache health check failed: {e}")
    
    response_status = status.HTTP_200_OK if health_status['status'] == 'healthy' else status.HTTP_503_SERVICE_UNAVAILABLE
    return Response(health_status, status=response_status)


@api_view(['GET'])
@permission_classes([AllowAny])
def service_info(request):
    """
    Service information endpoint.
    """
    return Response({
        'service': 'Data Analytics Service',
        'version': '1.0.0',
        'description': 'Microservice for data analytics, reporting, and dashboard insights',
        'features': [
            'Timetable efficiency analysis',
            'Lecturer and student satisfaction metrics',
            'Preference trend analysis',
            'Conflict detection and analysis',
            'Workload distribution analysis',
            'Room utilization tracking',
            'Automated report generation',
            'Real-time dashboard data',
            'Data visualization and charts',
            'Historical trend analysis'
        ],
        'capabilities': {
            'data_processing': {
                'pandas_version': pd.__version__,
                'numpy_version': np.__version__,
                'chunk_size': settings.PANDAS_CHUNK_SIZE
            },
            'visualization': {
                'matplotlib_backend': settings.MATPLOTLIB_BACKEND,
                'chart_dpi': settings.CHART_DPI,
                'default_size': f"{settings.CHART_DEFAULT_WIDTH}x{settings.CHART_DEFAULT_HEIGHT}"
            },
            'caching': {
                'enabled': True,
                'timeout': settings.ANALYTICS_CACHE_TIMEOUT,
                'dashboard_timeout': settings.DASHBOARD_CACHE_TIMEOUT
            },
            'reporting': {
                'output_dir': str(settings.REPORT_OUTPUT_DIR),
                'retention_days': settings.REPORT_RETENTION_DAYS,
                'max_concurrent': settings.MAX_CONCURRENT_REPORTS
            }
        },
        'endpoints': {
            'health': '/api/health/',
            'docs': '/api/docs/',
            'analytics': '/api/analytics/',
            'reports': '/api/reports/',
            'dashboard': '/api/dashboard/'
        },
        'data_sources': [
            'User Management Service',
            'Preference Collection Service',
            'Timetable Generation Service',
            'Notification Service'
        ],
        'timestamp': timezone.now().isoformat()
    })


@api_view(['GET'])
def service_stats(request):
    """
    Service statistics endpoint.
    """
    from analytics.models import TimetableAnalytics, SatisfactionMetric
    from reports.models import Report
    from core.models import AnalyticsMetric, DataSource
    from datetime import timedelta
    
    # Calculate statistics
    now = timezone.now()
    last_24h = now - timedelta(hours=24)
    last_7d = now - timedelta(days=7)
    
    stats = {
        'analytics': {
            'total_metrics': AnalyticsMetric.objects.count(),
            'metrics_last_24h': AnalyticsMetric.objects.filter(created_at__gte=last_24h).count(),
            'timetable_analyses': TimetableAnalytics.objects.count(),
            'satisfaction_metrics': SatisfactionMetric.objects.count(),
        },
        'reports': {
            'total_reports': Report.objects.count(),
            'reports_last_24h': Report.objects.filter(created_at__gte=last_24h).count(),
            'reports_last_7d': Report.objects.filter(created_at__gte=last_7d).count(),
            'by_status': {}
        },
        'data_sources': {
            'total_sources': DataSource.objects.count(),
            'active_sources': DataSource.objects.filter(is_enabled=True).count(),
            'sync_status': {}
        },
        'cache': {
            'cache_hits': 0,  # Would be tracked in production
            'cache_misses': 0,  # Would be tracked in production
            'cache_size': 0   # Would be tracked in production
        },
        'timestamp': now.isoformat()
    }
    
    # Get report status breakdown
    from reports.models import Report
    for status_choice in Report.STATUS_CHOICES:
        status_value = status_choice[0]
        count = Report.objects.filter(status=status_value).count()
        if count > 0:
            stats['reports']['by_status'][status_value] = count
    
    # Get data source sync status
    for source in DataSource.objects.all():
        stats['data_sources']['sync_status'][source.name] = {
            'status': source.sync_status,
            'last_sync': source.last_sync_at.isoformat() if source.last_sync_at else None,
            'records_synced': source.records_synced
        }
    
    return Response(stats)
