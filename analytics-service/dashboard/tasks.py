"""
Celery tasks for dashboard data processing.
"""
from celery import shared_task
from django.core.cache import cache
from django.conf import settings
from .services import DashboardService
import logging

logger = logging.getLogger(__name__)


@shared_task
def update_dashboard_cache():
    """
    Update dashboard cache with fresh data.
    """
    try:
        dashboard_service = DashboardService()
        
        # Update all dashboard data caches
        cache_updates = {}
        
        # Overview metrics
        try:
            overview_data = dashboard_service.get_overview_metrics()
            cache_updates['overview_metrics'] = len(overview_data)
        except Exception as e:
            logger.error(f"Failed to update overview metrics cache: {e}")
        
        # Efficiency trends
        try:
            trends_data = dashboard_service.get_efficiency_trends()
            cache_updates['efficiency_trends'] = len(trends_data.get('trends', []))
        except Exception as e:
            logger.error(f"Failed to update efficiency trends cache: {e}")
        
        # Satisfaction breakdown
        try:
            satisfaction_data = dashboard_service.get_satisfaction_breakdown()
            cache_updates['satisfaction_breakdown'] = len(satisfaction_data.get('breakdown', {}))
        except Exception as e:
            logger.error(f"Failed to update satisfaction breakdown cache: {e}")
        
        # Conflict insights
        try:
            conflict_data = dashboard_service.get_conflict_insights()
            cache_updates['conflict_insights'] = conflict_data.get('total_conflicts', 0)
        except Exception as e:
            logger.error(f"Failed to update conflict insights cache: {e}")
        
        # Preference analytics
        try:
            preference_data = dashboard_service.get_preference_analytics()
            cache_updates['preference_analytics'] = len(preference_data.get('analytics', []))
        except Exception as e:
            logger.error(f"Failed to update preference analytics cache: {e}")
        
        # System performance
        try:
            performance_data = dashboard_service.get_system_performance_metrics()
            cache_updates['system_performance'] = performance_data.get('analyses_last_24h', 0)
        except Exception as e:
            logger.error(f"Failed to update system performance cache: {e}")
        
        logger.info(f"Updated dashboard cache: {cache_updates}")
        
        return {
            'status': 'success',
            'cache_updates': cache_updates,
            'total_updates': len(cache_updates)
        }
        
    except Exception as e:
        logger.error(f"Failed to update dashboard cache: {e}")
        return {'status': 'failed', 'error': str(e)}


@shared_task
def generate_dashboard_insights():
    """
    Generate insights for dashboard display.
    """
    try:
        dashboard_service = DashboardService()
        
        # Get current data
        overview = dashboard_service.get_overview_metrics()
        conflicts = dashboard_service.get_conflict_insights()
        preferences = dashboard_service.get_preference_analytics()
        
        # Generate insights
        insights = []
        
        # Efficiency insights
        efficiency_score = overview.get('average_efficiency_score', 0)
        if efficiency_score < 70:
            insights.append({
                'type': 'warning',
                'category': 'efficiency',
                'title': 'Low System Efficiency',
                'message': f'Average efficiency score is {efficiency_score:.1f}%. Consider reviewing timetable generation parameters.',
                'priority': 'high'
            })
        elif efficiency_score > 90:
            insights.append({
                'type': 'success',
                'category': 'efficiency',
                'title': 'Excellent System Efficiency',
                'message': f'System is performing excellently with {efficiency_score:.1f}% efficiency.',
                'priority': 'info'
            })
        
        # Conflict insights
        total_conflicts = conflicts.get('total_conflicts', 0)
        if total_conflicts > 100:
            insights.append({
                'type': 'error',
                'category': 'conflicts',
                'title': 'High Conflict Rate',
                'message': f'{total_conflicts} conflicts detected. Immediate attention required.',
                'priority': 'critical'
            })
        elif total_conflicts > 50:
            insights.append({
                'type': 'warning',
                'category': 'conflicts',
                'title': 'Moderate Conflict Rate',
                'message': f'{total_conflicts} conflicts detected. Consider optimization.',
                'priority': 'medium'
            })
        
        # Satisfaction insights
        satisfaction_score = overview.get('average_satisfaction_score', 0)
        if satisfaction_score < 60:
            insights.append({
                'type': 'warning',
                'category': 'satisfaction',
                'title': 'Low User Satisfaction',
                'message': f'Average satisfaction is {satisfaction_score:.1f}%. Review user preferences and feedback.',
                'priority': 'high'
            })
        
        # Preference diversity insights
        preference_analytics = preferences.get('analytics', [])
        low_diversity_count = sum(1 for p in preference_analytics if p.get('average_diversity', 0) < 0.3)
        
        if low_diversity_count > 0:
            insights.append({
                'type': 'info',
                'category': 'preferences',
                'title': 'Low Preference Diversity',
                'message': f'{low_diversity_count} preference categories show low diversity. Users may need more options.',
                'priority': 'low'
            })
        
        # Cache insights
        cache.set('dashboard_insights', insights, settings.DASHBOARD_CACHE_TIMEOUT)
        
        logger.info(f"Generated {len(insights)} dashboard insights")
        
        return {
            'status': 'success',
            'insights_generated': len(insights),
            'insights': insights
        }
        
    except Exception as e:
        logger.error(f"Failed to generate dashboard insights: {e}")
        return {'status': 'failed', 'error': str(e)}


@shared_task
def calculate_dashboard_kpis():
    """
    Calculate key performance indicators for dashboard.
    """
    try:
        from analytics.models import TimetableAnalytics, SatisfactionMetric, ConflictAnalysis
        from django.db.models import Avg, Count, Sum
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        last_30_days = now - timedelta(days=30)
        last_7_days = now - timedelta(days=7)
        
        # Calculate KPIs
        kpis = {}
        
        # Efficiency KPIs
        recent_analytics = TimetableAnalytics.objects.filter(analysis_date__gte=last_30_days)
        if recent_analytics.exists():
            kpis['efficiency'] = {
                'current_avg': recent_analytics.aggregate(avg=Avg('overall_efficiency_score'))['avg'],
                'trend': 'stable',  # Would calculate actual trend
                'target': 85.0,
                'status': 'good'
            }
        
        # Conflict KPIs
        recent_conflicts = ConflictAnalysis.objects.filter(analysis_date__gte=last_30_days)
        total_conflicts = recent_conflicts.aggregate(total=Sum('total_conflicts'))['total'] or 0
        
        kpis['conflicts'] = {
            'total_conflicts': total_conflicts,
            'avg_per_timetable': total_conflicts / recent_conflicts.count() if recent_conflicts.count() > 0 else 0,
            'trend': 'decreasing',  # Would calculate actual trend
            'target': 20.0,
            'status': 'warning' if total_conflicts > 100 else 'good'
        }
        
        # Satisfaction KPIs
        recent_satisfaction = SatisfactionMetric.objects.filter(
            measurement_period_end__gte=last_30_days
        )
        if recent_satisfaction.exists():
            kpis['satisfaction'] = {
                'current_avg': recent_satisfaction.aggregate(avg=Avg('satisfaction_score'))['avg'],
                'by_entity_type': {},
                'trend': 'stable',
                'target': 75.0,
                'status': 'good'
            }
            
            # Satisfaction by entity type
            for entity_type in ['lecturer', 'student', 'department']:
                entity_satisfaction = recent_satisfaction.filter(entity_type=entity_type)
                if entity_satisfaction.exists():
                    kpis['satisfaction']['by_entity_type'][entity_type] = entity_satisfaction.aggregate(
                        avg=Avg('satisfaction_score')
                    )['avg']
        
        # System health KPI
        kpis['system_health'] = {
            'score': 85.0,  # Would calculate from various metrics
            'components': {
                'data_freshness': 90.0,
                'processing_speed': 85.0,
                'error_rate': 95.0,
                'availability': 99.0
            },
            'status': 'excellent'
        }
        
        # Usage KPIs
        kpis['usage'] = {
            'analyses_last_7d': TimetableAnalytics.objects.filter(
                created_at__gte=last_7_days
            ).count(),
            'active_users_7d': 0,  # Would calculate from user activity
            'reports_generated_7d': 0,  # Would get from reports
            'trend': 'increasing'
        }
        
        # Cache KPIs
        cache.set('dashboard_kpis', kpis, settings.DASHBOARD_CACHE_TIMEOUT)
        
        logger.info(f"Calculated dashboard KPIs: {list(kpis.keys())}")
        
        return {
            'status': 'success',
            'kpis_calculated': list(kpis.keys()),
            'kpis': kpis
        }
        
    except Exception as e:
        logger.error(f"Failed to calculate dashboard KPIs: {e}")
        return {'status': 'failed', 'error': str(e)}


@shared_task
def generate_dashboard_alerts():
    """
    Generate alerts for dashboard based on thresholds.
    """
    try:
        dashboard_service = DashboardService()
        
        # Get current metrics
        overview = dashboard_service.get_overview_metrics()
        performance = dashboard_service.get_system_performance_metrics()
        conflicts = dashboard_service.get_conflict_insights()
        
        alerts = []
        
        # Check efficiency threshold
        efficiency_score = overview.get('average_efficiency_score', 0)
        if efficiency_score < 70:
            alerts.append({
                'id': 'low_efficiency',
                'type': 'warning',
                'title': 'Low System Efficiency',
                'message': f'System efficiency has dropped to {efficiency_score:.1f}%',
                'threshold': 70,
                'current_value': efficiency_score,
                'severity': 'high' if efficiency_score < 60 else 'medium',
                'timestamp': timezone.now().isoformat()
            })
        
        # Check conflict threshold
        total_conflicts = conflicts.get('total_conflicts', 0)
        if total_conflicts > 50:
            alerts.append({
                'id': 'high_conflicts',
                'type': 'error',
                'title': 'High Conflict Count',
                'message': f'{total_conflicts} conflicts detected in recent timetables',
                'threshold': 50,
                'current_value': total_conflicts,
                'severity': 'critical' if total_conflicts > 100 else 'high',
                'timestamp': timezone.now().isoformat()
            })
        
        # Check system health
        health_score = overview.get('system_health_score', 0)
        if health_score < 80:
            alerts.append({
                'id': 'system_health',
                'type': 'warning',
                'title': 'System Health Degraded',
                'message': f'System health score is {health_score:.1f}%',
                'threshold': 80,
                'current_value': health_score,
                'severity': 'high' if health_score < 60 else 'medium',
                'timestamp': timezone.now().isoformat()
            })
        
        # Check recent activity
        recent_analyses = performance.get('analyses_last_24h', 0)
        if recent_analyses == 0:
            alerts.append({
                'id': 'no_recent_activity',
                'type': 'info',
                'title': 'No Recent Analysis Activity',
                'message': 'No timetable analyses performed in the last 24 hours',
                'threshold': 1,
                'current_value': recent_analyses,
                'severity': 'low',
                'timestamp': timezone.now().isoformat()
            })
        
        # Cache alerts
        cache.set('dashboard_alerts', alerts, settings.DASHBOARD_CACHE_TIMEOUT)
        
        logger.info(f"Generated {len(alerts)} dashboard alerts")
        
        return {
            'status': 'success',
            'alerts_generated': len(alerts),
            'alerts': alerts
        }
        
    except Exception as e:
        logger.error(f"Failed to generate dashboard alerts: {e}")
        return {'status': 'failed', 'error': str(e)}
