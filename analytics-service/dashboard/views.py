"""
Dashboard views for admin insights.
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.core.cache import cache
from django.utils import timezone
from .services import DashboardService
from .serializers import (
    DashboardOverviewSerializer, EfficiencyTrendsSerializer,
    SatisfactionBreakdownSerializer, ConflictInsightsSerializer,
    PreferenceAnalyticsSerializer, SystemPerformanceSerializer
)
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_overview(request):
    """
    Get dashboard overview with key metrics.
    """
    try:
        dashboard_service = DashboardService()
        overview_data = dashboard_service.get_overview_metrics()
        
        serializer = DashboardOverviewSerializer(overview_data)
        return Response(serializer.data)
        
    except Exception as e:
        logger.error(f"Failed to get dashboard overview: {e}")
        return Response({
            'error': 'Failed to get dashboard overview',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def efficiency_trends(request):
    """
    Get efficiency trends over time.
    """
    try:
        days = int(request.query_params.get('days', 30))
        
        dashboard_service = DashboardService()
        trends_data = dashboard_service.get_efficiency_trends(days)
        
        serializer = EfficiencyTrendsSerializer(trends_data)
        return Response(serializer.data)
        
    except Exception as e:
        logger.error(f"Failed to get efficiency trends: {e}")
        return Response({
            'error': 'Failed to get efficiency trends',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def satisfaction_breakdown(request):
    """
    Get satisfaction metrics breakdown.
    """
    try:
        dashboard_service = DashboardService()
        satisfaction_data = dashboard_service.get_satisfaction_breakdown()
        
        serializer = SatisfactionBreakdownSerializer(satisfaction_data)
        return Response(serializer.data)
        
    except Exception as e:
        logger.error(f"Failed to get satisfaction breakdown: {e}")
        return Response({
            'error': 'Failed to get satisfaction breakdown',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def conflict_insights(request):
    """
    Get conflict analysis insights.
    """
    try:
        dashboard_service = DashboardService()
        conflict_data = dashboard_service.get_conflict_insights()
        
        serializer = ConflictInsightsSerializer(conflict_data)
        return Response(serializer.data)
        
    except Exception as e:
        logger.error(f"Failed to get conflict insights: {e}")
        return Response({
            'error': 'Failed to get conflict insights',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def preference_analytics(request):
    """
    Get preference analytics and trends.
    """
    try:
        dashboard_service = DashboardService()
        preference_data = dashboard_service.get_preference_analytics()
        
        serializer = PreferenceAnalyticsSerializer(preference_data)
        return Response(serializer.data)
        
    except Exception as e:
        logger.error(f"Failed to get preference analytics: {e}")
        return Response({
            'error': 'Failed to get preference analytics',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def system_performance(request):
    """
    Get system performance metrics.
    """
    try:
        dashboard_service = DashboardService()
        performance_data = dashboard_service.get_system_performance_metrics()
        
        serializer = SystemPerformanceSerializer(performance_data)
        return Response(serializer.data)
        
    except Exception as e:
        logger.error(f"Failed to get system performance: {e}")
        return Response({
            'error': 'Failed to get system performance',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def chart_data(request):
    """
    Get chart data for dashboard visualizations.
    """
    try:
        chart_type = request.query_params.get('type')
        days = int(request.query_params.get('days', 30))
        
        dashboard_service = DashboardService()
        
        if chart_type == 'efficiency_trend':
            data = dashboard_service.get_efficiency_trends(days)
            chart_data = {
                'type': 'line',
                'data': {
                    'labels': [item['date'] for item in data.get('trends', [])],
                    'datasets': [
                        {
                            'label': 'Efficiency Score',
                            'data': [item['efficiency_score'] for item in data.get('trends', [])],
                            'borderColor': 'rgb(75, 192, 192)',
                            'backgroundColor': 'rgba(75, 192, 192, 0.2)'
                        },
                        {
                            'label': 'Conflict Rate',
                            'data': [item['conflict_rate'] for item in data.get('trends', [])],
                            'borderColor': 'rgb(255, 99, 132)',
                            'backgroundColor': 'rgba(255, 99, 132, 0.2)'
                        }
                    ]
                },
                'options': {
                    'responsive': True,
                    'scales': {
                        'y': {
                            'beginAtZero': True,
                            'max': 100
                        }
                    }
                }
            }
            
        elif chart_type == 'satisfaction_pie':
            data = dashboard_service.get_satisfaction_breakdown()
            breakdown = data.get('breakdown', {})
            
            labels = []
            values = []
            colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
            
            for i, (entity_type, metrics) in enumerate(breakdown.items()):
                if 'overall_satisfaction' in metrics:
                    labels.append(entity_type.replace('_', ' ').title())
                    values.append(metrics['overall_satisfaction']['average_score'])
            
            chart_data = {
                'type': 'pie',
                'data': {
                    'labels': labels,
                    'datasets': [{
                        'data': values,
                        'backgroundColor': colors[:len(values)]
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'legend': {
                            'position': 'bottom'
                        }
                    }
                }
            }
            
        elif chart_type == 'conflict_bar':
            data = dashboard_service.get_conflict_insights()
            conflict_types = data.get('conflict_types', {})
            
            chart_data = {
                'type': 'bar',
                'data': {
                    'labels': [ct.replace('_', ' ').title() for ct in conflict_types.keys()],
                    'datasets': [{
                        'label': 'Number of Conflicts',
                        'data': list(conflict_types.values()),
                        'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                        'borderColor': 'rgba(255, 99, 132, 1)',
                        'borderWidth': 1
                    }]
                },
                'options': {
                    'responsive': True,
                    'scales': {
                        'y': {
                            'beginAtZero': True
                        }
                    }
                }
            }
            
        else:
            return Response({
                'error': 'Invalid chart type',
                'available_types': ['efficiency_trend', 'satisfaction_pie', 'conflict_bar']
            }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(chart_data)
        
    except Exception as e:
        logger.error(f"Failed to get chart data: {e}")
        return Response({
            'error': 'Failed to get chart data',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_widgets(request):
    """
    Get data for dashboard widgets.
    """
    try:
        dashboard_service = DashboardService()
        
        # Get all widget data
        overview = dashboard_service.get_overview_metrics()
        performance = dashboard_service.get_system_performance_metrics()
        conflicts = dashboard_service.get_conflict_insights()
        
        widgets = {
            'efficiency_score': {
                'title': 'Average Efficiency',
                'value': overview.get('average_efficiency_score', 0),
                'unit': '%',
                'trend': 'up' if overview.get('average_efficiency_score', 0) > 75 else 'down',
                'color': 'success' if overview.get('average_efficiency_score', 0) > 75 else 'warning'
            },
            'total_conflicts': {
                'title': 'Total Conflicts',
                'value': conflicts.get('total_conflicts', 0),
                'unit': '',
                'trend': 'down' if conflicts.get('total_conflicts', 0) < 50 else 'up',
                'color': 'success' if conflicts.get('total_conflicts', 0) < 50 else 'danger'
            },
            'satisfaction_score': {
                'title': 'Avg Satisfaction',
                'value': overview.get('average_satisfaction_score', 0),
                'unit': '%',
                'trend': 'up' if overview.get('average_satisfaction_score', 0) > 70 else 'down',
                'color': 'success' if overview.get('average_satisfaction_score', 0) > 70 else 'warning'
            },
            'system_health': {
                'title': 'System Health',
                'value': overview.get('system_health_score', 0),
                'unit': '%',
                'trend': 'up' if overview.get('system_health_score', 0) > 80 else 'down',
                'color': 'success' if overview.get('system_health_score', 0) > 80 else 'warning'
            },
            'recent_analyses': {
                'title': 'Recent Analyses',
                'value': performance.get('analyses_last_24h', 0),
                'unit': '',
                'trend': performance.get('analysis_trend', 'stable'),
                'color': 'info'
            },
            'reports_generated': {
                'title': 'Reports Generated',
                'value': performance.get('reports_generated_24h', 0),
                'unit': '',
                'trend': performance.get('report_trend', 'stable'),
                'color': 'info'
            }
        }
        
        return Response({
            'widgets': widgets,
            'last_updated': timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Failed to get dashboard widgets: {e}")
        return Response({
            'error': 'Failed to get dashboard widgets',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def refresh_dashboard_cache(request):
    """
    Refresh dashboard cache data.
    """
    try:
        # Clear dashboard-related cache keys
        cache_keys = [
            'dashboard_overview_metrics',
            'dashboard_efficiency_trends_30',
            'dashboard_satisfaction_breakdown',
            'dashboard_conflict_insights',
            'dashboard_preference_analytics',
            'dashboard_system_performance'
        ]
        
        cache.delete_many(cache_keys)
        
        # Optionally trigger cache refresh
        if request.data.get('refresh_data', False):
            dashboard_service = DashboardService()
            dashboard_service.get_overview_metrics()
            dashboard_service.get_efficiency_trends()
            dashboard_service.get_satisfaction_breakdown()
            dashboard_service.get_conflict_insights()
            dashboard_service.get_preference_analytics()
            dashboard_service.get_system_performance_metrics()
        
        return Response({
            'message': 'Dashboard cache refreshed successfully',
            'cleared_keys': len(cache_keys),
            'timestamp': timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Failed to refresh dashboard cache: {e}")
        return Response({
            'error': 'Failed to refresh dashboard cache',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_export(request):
    """
    Export dashboard data for external use.
    """
    try:
        export_format = request.query_params.get('format', 'json')
        
        dashboard_service = DashboardService()
        
        # Collect all dashboard data
        dashboard_data = {
            'overview': dashboard_service.get_overview_metrics(),
            'efficiency_trends': dashboard_service.get_efficiency_trends(),
            'satisfaction_breakdown': dashboard_service.get_satisfaction_breakdown(),
            'conflict_insights': dashboard_service.get_conflict_insights(),
            'preference_analytics': dashboard_service.get_preference_analytics(),
            'system_performance': dashboard_service.get_system_performance_metrics(),
            'exported_at': timezone.now().isoformat(),
            'exported_by': request.user.email
        }
        
        if export_format == 'json':
            return Response(dashboard_data)
        
        elif export_format == 'csv':
            # For CSV, we'll flatten the data structure
            import pandas as pd
            from django.http import HttpResponse
            
            # Create a simple CSV with key metrics
            csv_data = {
                'Metric': [
                    'Average Efficiency Score',
                    'Total Conflicts Detected',
                    'Average Satisfaction Score',
                    'System Health Score',
                    'Recent Analyses (24h)',
                    'Reports Generated (24h)'
                ],
                'Value': [
                    dashboard_data['overview'].get('average_efficiency_score', 0),
                    dashboard_data['conflict_insights'].get('total_conflicts', 0),
                    dashboard_data['overview'].get('average_satisfaction_score', 0),
                    dashboard_data['overview'].get('system_health_score', 0),
                    dashboard_data['system_performance'].get('analyses_last_24h', 0),
                    dashboard_data['system_performance'].get('reports_generated_24h', 0)
                ]
            }
            
            df = pd.DataFrame(csv_data)
            
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="dashboard_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
            df.to_csv(response, index=False)
            
            return response
        
        else:
            return Response({
                'error': 'Unsupported export format',
                'supported_formats': ['json', 'csv']
            }, status=status.HTTP_400_BAD_REQUEST)
        
    except Exception as e:
        logger.error(f"Failed to export dashboard data: {e}")
        return Response({
            'error': 'Failed to export dashboard data',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
