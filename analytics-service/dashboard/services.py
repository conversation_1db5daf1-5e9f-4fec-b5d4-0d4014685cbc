"""
Dashboard services for admin insights.
"""
import pandas as pd
import numpy as np
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from django.db.models import Avg, <PERSON>, <PERSON>, <PERSON>, Q
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


class DashboardService:
    """
    Service for generating dashboard data and insights.
    """
    
    def __init__(self):
        self.cache_timeout = settings.DASHBOARD_CACHE_TIMEOUT
    
    def get_overview_metrics(self):
        """
        Get high-level overview metrics for the dashboard.
        """
        cache_key = 'dashboard_overview_metrics'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            from analytics.models import TimetableAnalytics, SatisfactionMetric, ConflictAnalysis
            from core.models import AnalyticsMetric
            
            # Calculate overview metrics
            now = timezone.now()
            last_30_days = now - timedelta(days=30)
            
            metrics = {
                'total_timetables_analyzed': TimetableAnalytics.objects.count(),
                'average_efficiency_score': TimetableAnalytics.objects.aggregate(
                    avg_efficiency=Avg('overall_efficiency_score')
                )['avg_efficiency'] or 0,
                'total_conflicts_detected': ConflictAnalysis.objects.aggregate(
                    total_conflicts=Count('total_conflicts')
                )['total_conflicts'] or 0,
                'average_satisfaction_score': SatisfactionMetric.objects.aggregate(
                    avg_satisfaction=Avg('satisfaction_score')
                )['avg_satisfaction'] or 0,
                'recent_analyses': TimetableAnalytics.objects.filter(
                    created_at__gte=last_30_days
                ).count(),
                'system_health_score': self._calculate_system_health_score(),
                'last_updated': now.isoformat()
            }
            
            # Cache the results
            cache.set(cache_key, metrics, self.cache_timeout)
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get overview metrics: {e}")
            return {}
    
    def get_efficiency_trends(self, days=30):
        """
        Get efficiency trends over time.
        """
        cache_key = f'dashboard_efficiency_trends_{days}'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            from analytics.models import TimetableAnalytics
            
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # Get efficiency data
            analytics = TimetableAnalytics.objects.filter(
                analysis_date__gte=start_date
            ).order_by('analysis_date')
            
            # Convert to DataFrame for analysis
            df = pd.DataFrame(list(analytics.values(
                'analysis_date', 'overall_efficiency_score', 'conflict_rate',
                'room_utilization_rate', 'lecturer_workload_balance_score'
            )))
            
            if df.empty:
                return {'trends': [], 'summary': {}}
            
            # Group by date and calculate daily averages
            df['date'] = pd.to_datetime(df['analysis_date']).dt.date
            daily_trends = df.groupby('date').agg({
                'overall_efficiency_score': 'mean',
                'conflict_rate': 'mean',
                'room_utilization_rate': 'mean',
                'lecturer_workload_balance_score': 'mean'
            }).reset_index()
            
            # Convert to list of dictionaries
            trends = []
            for _, row in daily_trends.iterrows():
                trends.append({
                    'date': row['date'].isoformat(),
                    'efficiency_score': round(row['overall_efficiency_score'], 2),
                    'conflict_rate': round(row['conflict_rate'], 2),
                    'room_utilization': round(row['room_utilization_rate'], 2),
                    'workload_balance': round(row['lecturer_workload_balance_score'], 2)
                })
            
            # Calculate summary statistics
            summary = {
                'avg_efficiency': round(df['overall_efficiency_score'].mean(), 2),
                'efficiency_trend': self._calculate_trend(df['overall_efficiency_score']),
                'avg_conflicts': round(df['conflict_rate'].mean(), 2),
                'conflict_trend': self._calculate_trend(df['conflict_rate']),
                'total_analyses': len(df)
            }
            
            result = {
                'trends': trends,
                'summary': summary
            }
            
            # Cache the results
            cache.set(cache_key, result, self.cache_timeout)
            return result
            
        except Exception as e:
            logger.error(f"Failed to get efficiency trends: {e}")
            return {'trends': [], 'summary': {}}
    
    def get_satisfaction_breakdown(self):
        """
        Get satisfaction metrics breakdown by entity type.
        """
        cache_key = 'dashboard_satisfaction_breakdown'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            from analytics.models import SatisfactionMetric
            
            # Get satisfaction data by entity type
            satisfaction_data = SatisfactionMetric.objects.values(
                'entity_type', 'metric_type'
            ).annotate(
                avg_satisfaction=Avg('satisfaction_score'),
                count=Count('id')
            ).order_by('entity_type', 'metric_type')
            
            # Organize data by entity type
            breakdown = {}
            for item in satisfaction_data:
                entity_type = item['entity_type']
                if entity_type not in breakdown:
                    breakdown[entity_type] = {}
                
                breakdown[entity_type][item['metric_type']] = {
                    'average_score': round(item['avg_satisfaction'], 2),
                    'count': item['count']
                }
            
            # Calculate overall averages
            overall_avg = SatisfactionMetric.objects.aggregate(
                avg_satisfaction=Avg('satisfaction_score')
            )['avg_satisfaction'] or 0
            
            result = {
                'breakdown': breakdown,
                'overall_average': round(overall_avg, 2),
                'last_updated': timezone.now().isoformat()
            }
            
            # Cache the results
            cache.set(cache_key, result, self.cache_timeout)
            return result
            
        except Exception as e:
            logger.error(f"Failed to get satisfaction breakdown: {e}")
            return {'breakdown': {}, 'overall_average': 0}
    
    def get_conflict_insights(self):
        """
        Get insights about the most common conflicts and problematic areas.
        """
        cache_key = 'dashboard_conflict_insights'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            from analytics.models import ConflictAnalysis
            
            # Get recent conflict analyses
            recent_conflicts = ConflictAnalysis.objects.filter(
                analysis_date__gte=timezone.now() - timedelta(days=30)
            )
            
            # Analyze conflict patterns
            conflict_types = {}
            total_conflicts = 0
            
            for analysis in recent_conflicts:
                total_conflicts += analysis.total_conflicts
                
                # Extract conflict patterns
                patterns = analysis.conflict_patterns
                for conflict_type, count in patterns.items():
                    if conflict_type not in conflict_types:
                        conflict_types[conflict_type] = 0
                    conflict_types[conflict_type] += count
            
            # Sort by frequency
            sorted_conflicts = sorted(
                conflict_types.items(),
                key=lambda x: x[1],
                reverse=True
            )
            
            # Get most problematic timetables
            problematic_timetables = recent_conflicts.order_by('-total_conflicts')[:5]
            
            # Calculate insights
            insights = []
            
            if sorted_conflicts:
                most_common = sorted_conflicts[0]
                insights.append({
                    'type': 'most_common_conflict',
                    'title': f'Most Common Conflict: {most_common[0].replace("_", " ").title()}',
                    'description': f'Accounts for {most_common[1]} conflicts ({(most_common[1]/total_conflicts*100):.1f}% of total)',
                    'severity': 'high' if most_common[1] > total_conflicts * 0.3 else 'medium'
                })
            
            if total_conflicts > 0:
                avg_conflicts_per_timetable = total_conflicts / len(recent_conflicts) if recent_conflicts else 0
                insights.append({
                    'type': 'conflict_rate',
                    'title': f'Average Conflicts per Timetable: {avg_conflicts_per_timetable:.1f}',
                    'description': f'Based on {len(recent_conflicts)} timetables analyzed in the last 30 days',
                    'severity': 'high' if avg_conflicts_per_timetable > 10 else 'medium' if avg_conflicts_per_timetable > 5 else 'low'
                })
            
            result = {
                'total_conflicts': total_conflicts,
                'conflict_types': dict(sorted_conflicts),
                'problematic_timetables': [
                    {
                        'timetable_id': analysis.timetable_id,
                        'total_conflicts': analysis.total_conflicts,
                        'analysis_date': analysis.analysis_date.isoformat()
                    }
                    for analysis in problematic_timetables
                ],
                'insights': insights,
                'last_updated': timezone.now().isoformat()
            }
            
            # Cache the results
            cache.set(cache_key, result, self.cache_timeout)
            return result
            
        except Exception as e:
            logger.error(f"Failed to get conflict insights: {e}")
            return {'total_conflicts': 0, 'conflict_types': {}, 'insights': []}
    
    def get_preference_analytics(self):
        """
        Get analytics about preference trends and patterns.
        """
        cache_key = 'dashboard_preference_analytics'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            from analytics.models import PreferenceTrend
            
            # Get recent preference trends
            recent_trends = PreferenceTrend.objects.filter(
                trend_period_end__gte=timezone.now() - timedelta(days=30)
            )
            
            # Analyze trends by type and role
            trend_summary = {}
            
            for trend in recent_trends:
                key = f"{trend.preference_type}_{trend.user_role}"
                if key not in trend_summary:
                    trend_summary[key] = {
                        'preference_type': trend.preference_type,
                        'user_role': trend.user_role,
                        'diversity_scores': [],
                        'stability_scores': [],
                        'most_popular_options': []
                    }
                
                trend_summary[key]['diversity_scores'].append(trend.diversity_index)
                trend_summary[key]['stability_scores'].append(trend.stability_score)
                trend_summary[key]['most_popular_options'].append(trend.most_popular_option)
            
            # Calculate averages and insights
            analytics = []
            for key, data in trend_summary.items():
                avg_diversity = np.mean(data['diversity_scores']) if data['diversity_scores'] else 0
                avg_stability = np.mean(data['stability_scores']) if data['stability_scores'] else 0
                
                # Find most common popular option
                popular_options = data['most_popular_options']
                most_common_popular = max(set(popular_options), key=popular_options.count) if popular_options else None
                
                analytics.append({
                    'preference_type': data['preference_type'],
                    'user_role': data['user_role'],
                    'average_diversity': round(avg_diversity, 3),
                    'average_stability': round(avg_stability, 2),
                    'most_popular_option': most_common_popular,
                    'trend_count': len(data['diversity_scores'])
                })
            
            # Generate insights
            insights = []
            
            # Find most diverse preferences
            if analytics:
                most_diverse = max(analytics, key=lambda x: x['average_diversity'])
                insights.append({
                    'type': 'diversity',
                    'title': f'Most Diverse Preferences: {most_diverse["preference_type"].replace("_", " ").title()}',
                    'description': f'{most_diverse["user_role"].title()}s show high diversity (score: {most_diverse["average_diversity"]:.3f})',
                    'severity': 'info'
                })
                
                # Find most stable preferences
                most_stable = max(analytics, key=lambda x: x['average_stability'])
                insights.append({
                    'type': 'stability',
                    'title': f'Most Stable Preferences: {most_stable["preference_type"].replace("_", " ").title()}',
                    'description': f'{most_stable["user_role"].title()}s show consistent preferences (stability: {most_stable["average_stability"]:.1f}%)',
                    'severity': 'info'
                })
            
            result = {
                'analytics': analytics,
                'insights': insights,
                'total_trends_analyzed': len(recent_trends),
                'last_updated': timezone.now().isoformat()
            }
            
            # Cache the results
            cache.set(cache_key, result, self.cache_timeout)
            return result
            
        except Exception as e:
            logger.error(f"Failed to get preference analytics: {e}")
            return {'analytics': [], 'insights': []}
    
    def get_system_performance_metrics(self):
        """
        Get system performance and usage metrics.
        """
        cache_key = 'dashboard_system_performance'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            from analytics.models import TimetableAnalytics
            from reports.models import Report
            
            now = timezone.now()
            last_24h = now - timedelta(hours=24)
            last_7d = now - timedelta(days=7)
            
            # Calculate performance metrics
            metrics = {
                'analyses_last_24h': TimetableAnalytics.objects.filter(
                    created_at__gte=last_24h
                ).count(),
                'analyses_last_7d': TimetableAnalytics.objects.filter(
                    created_at__gte=last_7d
                ).count(),
                'reports_generated_24h': Report.objects.filter(
                    completed_at__gte=last_24h,
                    status='completed'
                ).count(),
                'reports_generated_7d': Report.objects.filter(
                    completed_at__gte=last_7d,
                    status='completed'
                ).count(),
                'average_analysis_efficiency': TimetableAnalytics.objects.filter(
                    created_at__gte=last_7d
                ).aggregate(avg_efficiency=Avg('overall_efficiency_score'))['avg_efficiency'] or 0,
                'system_health_score': self._calculate_system_health_score(),
                'last_updated': now.isoformat()
            }
            
            # Calculate trends
            metrics['analysis_trend'] = self._calculate_activity_trend('analyses')
            metrics['report_trend'] = self._calculate_activity_trend('reports')
            
            # Cache the results
            cache.set(cache_key, metrics, self.cache_timeout)
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get system performance metrics: {e}")
            return {}
    
    def _calculate_system_health_score(self):
        """
        Calculate overall system health score based on various metrics.
        """
        try:
            from analytics.models import TimetableAnalytics, ConflictAnalysis
            
            # Get recent data
            recent_analytics = TimetableAnalytics.objects.filter(
                analysis_date__gte=timezone.now() - timedelta(days=7)
            )
            
            if not recent_analytics.exists():
                return 50.0  # Neutral score if no recent data
            
            # Calculate component scores
            avg_efficiency = recent_analytics.aggregate(
                avg=Avg('overall_efficiency_score')
            )['avg'] or 0
            
            avg_conflicts = ConflictAnalysis.objects.filter(
                analysis_date__gte=timezone.now() - timedelta(days=7)
            ).aggregate(avg=Avg('total_conflicts'))['avg'] or 0
            
            # Normalize scores (0-100)
            efficiency_score = min(100, max(0, avg_efficiency))
            conflict_score = max(0, 100 - (avg_conflicts * 2))  # Fewer conflicts = higher score
            
            # Weighted average
            health_score = (efficiency_score * 0.6) + (conflict_score * 0.4)
            
            return round(health_score, 1)
            
        except Exception as e:
            logger.error(f"Failed to calculate system health score: {e}")
            return 50.0
    
    def _calculate_trend(self, series):
        """
        Calculate trend direction (up, down, stable) for a data series.
        """
        try:
            if len(series) < 2:
                return 'stable'
            
            # Calculate linear regression slope
            x = np.arange(len(series))
            y = series.values
            
            # Remove NaN values
            mask = ~np.isnan(y)
            if np.sum(mask) < 2:
                return 'stable'
            
            x = x[mask]
            y = y[mask]
            
            slope = np.polyfit(x, y, 1)[0]
            
            # Determine trend based on slope
            if slope > 0.1:
                return 'up'
            elif slope < -0.1:
                return 'down'
            else:
                return 'stable'
                
        except Exception as e:
            logger.error(f"Failed to calculate trend: {e}")
            return 'stable'
    
    def _calculate_activity_trend(self, activity_type):
        """
        Calculate activity trend (analyses or reports).
        """
        try:
            now = timezone.now()
            
            if activity_type == 'analyses':
                from analytics.models import TimetableAnalytics
                
                current_week = TimetableAnalytics.objects.filter(
                    created_at__gte=now - timedelta(days=7)
                ).count()
                
                previous_week = TimetableAnalytics.objects.filter(
                    created_at__gte=now - timedelta(days=14),
                    created_at__lt=now - timedelta(days=7)
                ).count()
                
            elif activity_type == 'reports':
                from reports.models import Report
                
                current_week = Report.objects.filter(
                    completed_at__gte=now - timedelta(days=7),
                    status='completed'
                ).count()
                
                previous_week = Report.objects.filter(
                    completed_at__gte=now - timedelta(days=14),
                    completed_at__lt=now - timedelta(days=7),
                    status='completed'
                ).count()
            else:
                return 'stable'
            
            # Calculate percentage change
            if previous_week == 0:
                return 'up' if current_week > 0 else 'stable'
            
            change_percent = ((current_week - previous_week) / previous_week) * 100
            
            if change_percent > 10:
                return 'up'
            elif change_percent < -10:
                return 'down'
            else:
                return 'stable'
                
        except Exception as e:
            logger.error(f"Failed to calculate activity trend: {e}")
            return 'stable'
