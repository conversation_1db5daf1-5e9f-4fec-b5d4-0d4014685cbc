"""
Serializers for dashboard data.
"""
from rest_framework import serializers


class DashboardOverviewSerializer(serializers.Serializer):
    """
    Serializer for dashboard overview metrics.
    """
    total_timetables_analyzed = serializers.IntegerField()
    average_efficiency_score = serializers.FloatField()
    total_conflicts_detected = serializers.IntegerField()
    average_satisfaction_score = serializers.FloatField()
    recent_analyses = serializers.IntegerField()
    system_health_score = serializers.FloatField()
    last_updated = serializers.CharField()


class EfficiencyTrendItemSerializer(serializers.Serializer):
    """
    Serializer for individual efficiency trend data point.
    """
    date = serializers.CharField()
    efficiency_score = serializers.FloatField()
    conflict_rate = serializers.FloatField()
    room_utilization = serializers.FloatField()
    workload_balance = serializers.FloatField()


class EfficiencyTrendSummarySerializer(serializers.Serializer):
    """
    Serializer for efficiency trend summary.
    """
    avg_efficiency = serializers.FloatField()
    efficiency_trend = serializers.CharField()
    avg_conflicts = serializers.FloatField()
    conflict_trend = serializers.CharField()
    total_analyses = serializers.IntegerField()


class EfficiencyTrendsSerializer(serializers.Serializer):
    """
    Serializer for efficiency trends response.
    """
    trends = EfficiencyTrendItemSerializer(many=True)
    summary = EfficiencyTrendSummarySerializer()


class SatisfactionMetricBreakdownSerializer(serializers.Serializer):
    """
    Serializer for satisfaction metric breakdown.
    """
    average_score = serializers.FloatField()
    count = serializers.IntegerField()


class SatisfactionBreakdownSerializer(serializers.Serializer):
    """
    Serializer for satisfaction breakdown response.
    """
    breakdown = serializers.DictField(
        child=serializers.DictField(
            child=SatisfactionMetricBreakdownSerializer()
        )
    )
    overall_average = serializers.FloatField()
    last_updated = serializers.CharField()


class ConflictInsightSerializer(serializers.Serializer):
    """
    Serializer for individual conflict insight.
    """
    type = serializers.CharField()
    title = serializers.CharField()
    description = serializers.CharField()
    severity = serializers.CharField()


class ProblematicTimetableSerializer(serializers.Serializer):
    """
    Serializer for problematic timetable data.
    """
    timetable_id = serializers.CharField()
    total_conflicts = serializers.IntegerField()
    analysis_date = serializers.CharField()


class ConflictInsightsSerializer(serializers.Serializer):
    """
    Serializer for conflict insights response.
    """
    total_conflicts = serializers.IntegerField()
    conflict_types = serializers.DictField(child=serializers.IntegerField())
    problematic_timetables = ProblematicTimetableSerializer(many=True)
    insights = ConflictInsightSerializer(many=True)
    last_updated = serializers.CharField()


class PreferenceAnalyticsItemSerializer(serializers.Serializer):
    """
    Serializer for individual preference analytics item.
    """
    preference_type = serializers.CharField()
    user_role = serializers.CharField()
    average_diversity = serializers.FloatField()
    average_stability = serializers.FloatField()
    most_popular_option = serializers.CharField(allow_null=True)
    trend_count = serializers.IntegerField()


class PreferenceInsightSerializer(serializers.Serializer):
    """
    Serializer for preference insight.
    """
    type = serializers.CharField()
    title = serializers.CharField()
    description = serializers.CharField()
    severity = serializers.CharField()


class PreferenceAnalyticsSerializer(serializers.Serializer):
    """
    Serializer for preference analytics response.
    """
    analytics = PreferenceAnalyticsItemSerializer(many=True)
    insights = PreferenceInsightSerializer(many=True)
    total_trends_analyzed = serializers.IntegerField()
    last_updated = serializers.CharField()


class SystemPerformanceSerializer(serializers.Serializer):
    """
    Serializer for system performance metrics.
    """
    analyses_last_24h = serializers.IntegerField()
    analyses_last_7d = serializers.IntegerField()
    reports_generated_24h = serializers.IntegerField()
    reports_generated_7d = serializers.IntegerField()
    average_analysis_efficiency = serializers.FloatField()
    system_health_score = serializers.FloatField()
    analysis_trend = serializers.CharField()
    report_trend = serializers.CharField()
    last_updated = serializers.CharField()


class DashboardWidgetSerializer(serializers.Serializer):
    """
    Serializer for dashboard widget data.
    """
    title = serializers.CharField()
    value = serializers.FloatField()
    unit = serializers.CharField()
    trend = serializers.CharField()
    color = serializers.CharField()


class DashboardWidgetsSerializer(serializers.Serializer):
    """
    Serializer for dashboard widgets response.
    """
    widgets = serializers.DictField(child=DashboardWidgetSerializer())
    last_updated = serializers.CharField()


class ChartDatasetSerializer(serializers.Serializer):
    """
    Serializer for chart dataset.
    """
    label = serializers.CharField()
    data = serializers.ListField(child=serializers.FloatField())
    borderColor = serializers.CharField(required=False)
    backgroundColor = serializers.CharField(required=False)


class ChartDataSerializer(serializers.Serializer):
    """
    Serializer for chart data.
    """
    labels = serializers.ListField(child=serializers.CharField())
    datasets = ChartDatasetSerializer(many=True)


class ChartOptionsSerializer(serializers.Serializer):
    """
    Serializer for chart options.
    """
    responsive = serializers.BooleanField(default=True)
    # Additional chart options would be defined here as needed


class ChartSerializer(serializers.Serializer):
    """
    Serializer for complete chart configuration.
    """
    type = serializers.CharField()
    data = ChartDataSerializer()
    options = serializers.DictField(required=False)


class DashboardExportSerializer(serializers.Serializer):
    """
    Serializer for dashboard export data.
    """
    overview = DashboardOverviewSerializer()
    efficiency_trends = EfficiencyTrendsSerializer()
    satisfaction_breakdown = SatisfactionBreakdownSerializer()
    conflict_insights = ConflictInsightsSerializer()
    preference_analytics = PreferenceAnalyticsSerializer()
    system_performance = SystemPerformanceSerializer()
    exported_at = serializers.CharField()
    exported_by = serializers.CharField()


class CacheRefreshSerializer(serializers.Serializer):
    """
    Serializer for cache refresh response.
    """
    message = serializers.CharField()
    cleared_keys = serializers.IntegerField()
    timestamp = serializers.CharField()
