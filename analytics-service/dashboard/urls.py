"""
URLs configuration for dashboard app.
"""
from django.urls import path
from . import views

app_name = 'dashboard'

urlpatterns = [
    # Dashboard overview
    path('overview/', views.dashboard_overview, name='overview'),
    
    # Specific dashboard sections
    path('efficiency-trends/', views.efficiency_trends, name='efficiency-trends'),
    path('satisfaction-breakdown/', views.satisfaction_breakdown, name='satisfaction-breakdown'),
    path('conflict-insights/', views.conflict_insights, name='conflict-insights'),
    path('preference-analytics/', views.preference_analytics, name='preference-analytics'),
    path('system-performance/', views.system_performance, name='system-performance'),
    
    # Chart data
    path('chart-data/', views.chart_data, name='chart-data'),
    
    # Widgets
    path('widgets/', views.dashboard_widgets, name='widgets'),
    
    # Utilities
    path('refresh-cache/', views.refresh_dashboard_cache, name='refresh-cache'),
    path('export/', views.dashboard_export, name='export'),
]
