"""
URLs configuration for reports app.
"""
from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

app_name = 'reports'

# Create router for viewsets
router = DefaultRouter()
router.register(r'templates', views.ReportTemplateViewSet, basename='report-template')
router.register(r'reports', views.ReportViewSet, basename='report')
router.register(r'schedules', views.ReportScheduleViewSet, basename='report-schedule')

urlpatterns = [
    # ViewSet URLs
    path('', include(router.urls)),
    
    # Additional endpoints
    path('generate-custom/', views.generate_custom_report, name='generate-custom'),
    path('statistics/', views.report_statistics, name='statistics'),
]
