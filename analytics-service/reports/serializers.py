"""
Serializers for reports models.
"""
from rest_framework import serializers
from .models import ReportTemplate, Report, ReportSchedule
from core.models import User


class ReportTemplateSerializer(serializers.ModelSerializer):
    """
    Serializer for ReportTemplate model.
    """
    template_type_display = serializers.CharField(source='get_template_type_display', read_only=True)
    output_format_display = serializers.CharField(source='get_output_format_display', read_only=True)
    created_by_name = serializers.CharField(source='created_by.full_name', read_only=True)
    
    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'name', 'display_name', 'description', 'template_type',
            'template_type_display', 'output_format', 'output_format_display',
            'is_default', 'is_public', 'created_by', 'created_by_name',
            'template_content', 'css_content', 'default_parameters',
            'available_filters', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']
        extra_kwargs = {
            'template_content': {'write_only': True},
            'css_content': {'write_only': True}
        }


class ReportSerializer(serializers.ModelSerializer):
    """
    Serializer for Report model.
    """
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    template_name = serializers.CharField(source='template.display_name', read_only=True)
    template_type = serializers.CharField(source='template.template_type', read_only=True)
    output_format = serializers.CharField(source='template.output_format', read_only=True)
    requested_by_name = serializers.CharField(source='requested_by.full_name', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.full_name', read_only=True)
    filename = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = Report
        fields = [
            'id', 'name', 'description', 'template', 'template_name',
            'template_type', 'output_format', 'status', 'status_display',
            'requested_by', 'requested_by_name', 'generated_by', 'generated_by_name',
            'requested_at', 'started_at', 'completed_at', 'expires_at',
            'parameters', 'filters', 'file_path', 'filename', 'file_size',
            'download_count', 'generation_time_seconds', 'records_processed',
            'error_message', 'is_public', 'is_expired', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'status', 'requested_by', 'generated_by', 'requested_at',
            'started_at', 'completed_at', 'expires_at', 'file_path',
            'file_size', 'download_count', 'generation_time_seconds',
            'records_processed', 'error_message', 'created_at', 'updated_at'
        ]


class ReportCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating reports.
    """
    class Meta:
        model = Report
        fields = [
            'name', 'description', 'template', 'parameters', 'filters', 'is_public'
        ]
    
    def validate_template(self, value):
        """Validate that template exists and is accessible."""
        if not value.is_public and value.created_by != self.context['request'].user:
            if not self.context['request'].user.is_staff:
                raise serializers.ValidationError("You don't have access to this template")
        return value


class ReportGenerationStatusSerializer(serializers.Serializer):
    """
    Serializer for report generation status.
    """
    status_counts = serializers.DictField(child=serializers.IntegerField())
    recent_reports_24h = serializers.IntegerField()
    average_generation_time_seconds = serializers.FloatField(allow_null=True)
    total_reports = serializers.IntegerField()


class ReportScheduleSerializer(serializers.ModelSerializer):
    """
    Serializer for ReportSchedule model.
    """
    frequency_display = serializers.CharField(source='get_frequency_display', read_only=True)
    template_name = serializers.CharField(source='template.display_name', read_only=True)
    recipients_count = serializers.IntegerField(source='recipients.count', read_only=True)
    
    class Meta:
        model = ReportSchedule
        fields = [
            'id', 'name', 'description', 'template', 'template_name',
            'frequency', 'frequency_display', 'is_enabled', 'recipients',
            'recipients_count', 'parameters', 'filters', 'last_run_at',
            'next_run_at', 'run_count', 'auto_email', 'retention_days',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_run_at', 'next_run_at', 'run_count', 'created_at', 'updated_at'
        ]


class ReportStatisticsSerializer(serializers.Serializer):
    """
    Serializer for report statistics.
    """
    total_reports = serializers.IntegerField()
    completed_reports = serializers.IntegerField()
    failed_reports = serializers.IntegerField()
    success_rate = serializers.FloatField()
    recent_reports_7d = serializers.IntegerField()
    average_generation_time_seconds = serializers.FloatField(allow_null=True)
    total_file_size_bytes = serializers.IntegerField()
    reports_by_type = serializers.ListField(
        child=serializers.DictField()
    )
    reports_by_format = serializers.ListField(
        child=serializers.DictField()
    )
    last_updated = serializers.CharField()


class CustomReportRequestSerializer(serializers.Serializer):
    """
    Serializer for custom report generation request.
    """
    template_id = serializers.UUIDField()
    name = serializers.CharField(max_length=255)
    description = serializers.CharField(required=False, allow_blank=True)
    parameters = serializers.DictField(default=dict)
    filters = serializers.DictField(default=dict)
    
    def validate_template_id(self, value):
        """Validate that template exists."""
        try:
            template = ReportTemplate.objects.get(id=value)
            if not template.is_public and template.created_by != self.context['request'].user:
                if not self.context['request'].user.is_staff:
                    raise serializers.ValidationError("You don't have access to this template")
            return value
        except ReportTemplate.DoesNotExist:
            raise serializers.ValidationError("Template not found")


class ReportShareSerializer(serializers.Serializer):
    """
    Serializer for sharing reports.
    """
    user_ids = serializers.ListField(
        child=serializers.UUIDField(),
        min_length=1
    )
    
    def validate_user_ids(self, value):
        """Validate that all user IDs exist."""
        existing_users = User.objects.filter(id__in=value).count()
        if existing_users != len(value):
            raise serializers.ValidationError("Some user IDs are invalid")
        return value


class UpcomingScheduleSerializer(serializers.Serializer):
    """
    Serializer for upcoming scheduled reports.
    """
    upcoming_schedules = ReportScheduleSerializer(many=True)
    period_hours = serializers.IntegerField()
    count = serializers.IntegerField()


class ReportTemplatesByTypeSerializer(serializers.Serializer):
    """
    Serializer for templates grouped by type.
    """
    id = serializers.UUIDField()
    name = serializers.CharField()
    display_name = serializers.CharField()
    output_format = serializers.CharField()
    is_default = serializers.BooleanField()
