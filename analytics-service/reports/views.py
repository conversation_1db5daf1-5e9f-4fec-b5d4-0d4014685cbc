"""
Views for report generation and management.
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.http import FileResponse, Http404
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from .models import ReportTemplate, Report, ReportSchedule
from .serializers import (
    ReportTemplateSerializer, ReportSerializer, ReportScheduleSerializer,
    ReportCreateSerializer, ReportGenerationStatusSerializer
)
from .services import ReportGeneratorService
from .tasks import generate_report_task, process_scheduled_reports
import os
import logging

logger = logging.getLogger(__name__)


class ReportTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing report templates.
    """
    queryset = ReportTemplate.objects.all()
    serializer_class = ReportTemplateSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['template_type', 'output_format', 'is_default', 'is_public']
    search_fields = ['name', 'display_name', 'description']
    ordering_fields = ['name', 'template_type', 'created_at']
    ordering = ['template_type', 'name']
    
    def perform_create(self, serializer):
        """Set created_by to current user."""
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def test_generation(self, request, pk=None):
        """Test report generation with sample data."""
        template = self.get_object()
        
        # Sample parameters and filters
        sample_parameters = {
            'title': f'Test {template.display_name}',
            'include_charts': True,
            'date_format': 'YYYY-MM-DD'
        }
        
        sample_filters = {
            'date_range': {
                'start': (timezone.now() - timedelta(days=30)).isoformat(),
                'end': timezone.now().isoformat()
            }
        }
        
        try:
            generator = ReportGeneratorService()
            result = generator.generate_report(template, sample_parameters, sample_filters)
            
            return Response({
                'success': True,
                'message': 'Test report generated successfully',
                'file_size': result['file_size'],
                'records_processed': result['records_processed']
            })
            
        except Exception as e:
            logger.error(f"Failed to generate test report: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """Get templates grouped by type."""
        templates_by_type = {}
        
        for template in self.get_queryset():
            template_type = template.template_type
            if template_type not in templates_by_type:
                templates_by_type[template_type] = []
            
            templates_by_type[template_type].append({
                'id': template.id,
                'name': template.name,
                'display_name': template.display_name,
                'output_format': template.output_format,
                'is_default': template.is_default
            })
        
        return Response(templates_by_type)


class ReportViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing reports.
    """
    serializer_class = ReportSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'template__template_type', 'template__output_format']
    search_fields = ['name', 'description']
    ordering_fields = ['requested_at', 'completed_at', 'name']
    ordering = ['-requested_at']
    
    def get_queryset(self):
        """Filter reports based on user permissions."""
        user = self.request.user
        
        if user.is_superuser or user.role in ['admin', 'superadmin']:
            return Report.objects.all()
        else:
            # Users can see their own reports and public reports
            return Report.objects.filter(
                models.Q(requested_by=user) | 
                models.Q(is_public=True) |
                models.Q(shared_with=user)
            ).distinct()
    
    def get_serializer_class(self):
        if self.action == 'create':
            return ReportCreateSerializer
        elif self.action in ['generation_status', 'queue_status']:
            return ReportGenerationStatusSerializer
        return ReportSerializer
    
    def perform_create(self, serializer):
        """Set requested_by to current user and queue report generation."""
        report = serializer.save(requested_by=self.request.user)
        
        # Queue report generation
        generate_report_task.delay(str(report.id))
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download generated report file."""
        report = self.get_object()
        
        # Check if user can download this report
        if (report.requested_by != request.user and 
            not report.is_public and 
            request.user not in report.shared_with.all() and
            not request.user.is_staff):
            return Response(
                {'error': 'You do not have permission to download this report'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if report.status != 'completed':
            return Response(
                {'error': 'Report is not ready for download'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if report.is_expired:
            return Response(
                {'error': 'Report has expired'},
                status=status.HTTP_410_GONE
            )
        
        if not report.file_path or not os.path.exists(report.file_path):
            return Response(
                {'error': 'Report file not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Mark as downloaded
        report.mark_as_downloaded()
        
        # Return file response
        response = FileResponse(
            open(report.file_path, 'rb'),
            as_attachment=True,
            filename=report.filename
        )
        
        return response
    
    @action(detail=True, methods=['post'])
    def share(self, request, pk=None):
        """Share report with other users."""
        report = self.get_object()
        
        # Check if user can share this report
        if report.requested_by != request.user and not request.user.is_staff:
            return Response(
                {'error': 'You can only share your own reports'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        user_ids = request.data.get('user_ids', [])
        if not user_ids:
            return Response(
                {'error': 'No user IDs provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Add users to shared_with
        from core.models import User
        users = User.objects.filter(id__in=user_ids)
        report.shared_with.add(*users)
        
        return Response({
            'message': f'Report shared with {len(users)} users',
            'shared_with_count': report.shared_with.count()
        })
    
    @action(detail=True, methods=['post'])
    def regenerate(self, request, pk=None):
        """Regenerate an existing report."""
        report = self.get_object()
        
        # Check if user can regenerate this report
        if report.requested_by != request.user and not request.user.is_staff:
            return Response(
                {'error': 'You can only regenerate your own reports'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Create new report with same parameters
        new_report = Report.objects.create(
            name=f"{report.name} (Regenerated)",
            description=report.description,
            template=report.template,
            requested_by=request.user,
            parameters=report.parameters,
            filters=report.filters
        )
        
        # Queue generation
        generate_report_task.delay(str(new_report.id))
        
        return Response({
            'message': 'Report regeneration queued',
            'new_report_id': str(new_report.id)
        }, status=status.HTTP_202_ACCEPTED)
    
    @action(detail=False, methods=['get'])
    def my_reports(self, request):
        """Get current user's reports."""
        user_reports = self.get_queryset().filter(requested_by=request.user)
        
        page = self.paginate_queryset(user_reports)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(user_reports, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def generation_status(self, request):
        """Get report generation status summary."""
        queryset = self.get_queryset()
        
        # Count by status
        status_counts = {}
        for status_choice in Report.STATUS_CHOICES:
            status_value = status_choice[0]
            count = queryset.filter(status=status_value).count()
            if count > 0:
                status_counts[status_value] = count
        
        # Recent activity
        recent_reports = queryset.filter(
            requested_at__gte=timezone.now() - timedelta(hours=24)
        ).count()
        
        # Average generation time
        completed_reports = queryset.filter(
            status='completed',
            generation_time_seconds__isnull=False
        )
        
        avg_generation_time = None
        if completed_reports.exists():
            from django.db.models import Avg
            avg_time = completed_reports.aggregate(
                avg_time=Avg('generation_time_seconds')
            )['avg_time']
            avg_generation_time = round(avg_time, 2) if avg_time else None
        
        return Response({
            'status_counts': status_counts,
            'recent_reports_24h': recent_reports,
            'average_generation_time_seconds': avg_generation_time,
            'total_reports': queryset.count()
        })


class ReportScheduleViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing scheduled reports.
    """
    queryset = ReportSchedule.objects.all()
    serializer_class = ReportScheduleSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['frequency', 'is_enabled', 'template__template_type']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'frequency', 'next_run_at']
    ordering = ['next_run_at']
    
    @action(detail=True, methods=['post'])
    def run_now(self, request, pk=None):
        """Run scheduled report immediately."""
        schedule = self.get_object()
        
        if not schedule.is_enabled:
            return Response(
                {'error': 'Schedule is disabled'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Queue scheduled report processing
        task = process_scheduled_reports.delay(str(schedule.id))
        
        return Response({
            'message': 'Scheduled report queued for immediate execution',
            'task_id': task.id,
            'schedule_id': str(schedule.id)
        }, status=status.HTTP_202_ACCEPTED)
    
    @action(detail=True, methods=['post'])
    def enable(self, request, pk=None):
        """Enable scheduled report."""
        schedule = self.get_object()
        schedule.is_enabled = True
        schedule.update_next_run()
        schedule.save()
        
        return Response({
            'message': 'Schedule enabled',
            'next_run_at': schedule.next_run_at
        })
    
    @action(detail=True, methods=['post'])
    def disable(self, request, pk=None):
        """Disable scheduled report."""
        schedule = self.get_object()
        schedule.is_enabled = False
        schedule.save()
        
        return Response({'message': 'Schedule disabled'})
    
    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """Get upcoming scheduled reports."""
        hours = int(request.query_params.get('hours', 24))
        cutoff_time = timezone.now() + timedelta(hours=hours)
        
        upcoming_schedules = self.get_queryset().filter(
            is_enabled=True,
            next_run_at__lte=cutoff_time,
            next_run_at__gte=timezone.now()
        ).order_by('next_run_at')
        
        serializer = self.get_serializer(upcoming_schedules, many=True)
        return Response({
            'upcoming_schedules': serializer.data,
            'period_hours': hours,
            'count': len(serializer.data)
        })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def generate_custom_report(request):
    """
    Generate a custom report with provided parameters.
    """
    try:
        template_id = request.data.get('template_id')
        parameters = request.data.get('parameters', {})
        filters = request.data.get('filters', {})
        name = request.data.get('name', 'Custom Report')
        description = request.data.get('description', '')
        
        if not template_id:
            return Response(
                {'error': 'Template ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            template = ReportTemplate.objects.get(id=template_id)
        except ReportTemplate.DoesNotExist:
            return Response(
                {'error': 'Template not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Create report
        report = Report.objects.create(
            name=name,
            description=description,
            template=template,
            requested_by=request.user,
            parameters=parameters,
            filters=filters
        )
        
        # Queue generation
        task = generate_report_task.delay(str(report.id))
        
        return Response({
            'message': 'Custom report generation queued',
            'report_id': str(report.id),
            'task_id': task.id
        }, status=status.HTTP_202_ACCEPTED)
        
    except Exception as e:
        logger.error(f"Failed to generate custom report: {e}")
        return Response({
            'error': 'Failed to generate custom report',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def report_statistics(request):
    """
    Get report generation statistics.
    """
    try:
        from django.db.models import Count, Avg, Sum
        
        # Overall statistics
        total_reports = Report.objects.count()
        completed_reports = Report.objects.filter(status='completed').count()
        failed_reports = Report.objects.filter(status='failed').count()
        
        # Recent activity (last 7 days)
        recent_date = timezone.now() - timedelta(days=7)
        recent_reports = Report.objects.filter(requested_at__gte=recent_date).count()
        
        # Average generation time
        avg_generation_time = Report.objects.filter(
            status='completed',
            generation_time_seconds__isnull=False
        ).aggregate(avg_time=Avg('generation_time_seconds'))['avg_time']
        
        # Reports by template type
        reports_by_type = Report.objects.values(
            'template__template_type'
        ).annotate(count=Count('id')).order_by('-count')
        
        # Reports by output format
        reports_by_format = Report.objects.values(
            'template__output_format'
        ).annotate(count=Count('id')).order_by('-count')
        
        # Total file size
        total_file_size = Report.objects.filter(
            status='completed',
            file_size__isnull=False
        ).aggregate(total_size=Sum('file_size'))['total_size'] or 0
        
        # Success rate
        success_rate = (completed_reports / total_reports * 100) if total_reports > 0 else 0
        
        return Response({
            'total_reports': total_reports,
            'completed_reports': completed_reports,
            'failed_reports': failed_reports,
            'success_rate': round(success_rate, 2),
            'recent_reports_7d': recent_reports,
            'average_generation_time_seconds': round(avg_generation_time, 2) if avg_generation_time else None,
            'total_file_size_bytes': total_file_size,
            'reports_by_type': list(reports_by_type),
            'reports_by_format': list(reports_by_format),
            'last_updated': timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Failed to get report statistics: {e}")
        return Response({
            'error': 'Failed to get report statistics',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
