"""
Report generation services.
"""
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from django.conf import settings
from django.template import Template, Context
from django.utils import timezone
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import json
import logging

logger = logging.getLogger(__name__)

# Set matplotlib backend
plt.switch_backend(settings.MATPLOTLIB_BACKEND)


class ReportGeneratorService:
    """
    Service for generating reports in various formats.
    """
    
    def __init__(self):
        self.output_dir = settings.REPORT_OUTPUT_DIR
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_report(self, report_template, parameters, filters):
        """
        Generate a report based on template and parameters.
        """
        try:
            # Collect data based on filters
            data = self._collect_report_data(report_template.template_type, filters)
            
            if not data:
                raise ValueError("No data available for report generation")
            
            # Generate report based on output format
            if report_template.output_format == 'pdf':
                return self._generate_pdf_report(report_template, data, parameters)
            elif report_template.output_format == 'excel':
                return self._generate_excel_report(report_template, data, parameters)
            elif report_template.output_format == 'csv':
                return self._generate_csv_report(report_template, data, parameters)
            elif report_template.output_format == 'json':
                return self._generate_json_report(report_template, data, parameters)
            elif report_template.output_format == 'html':
                return self._generate_html_report(report_template, data, parameters)
            else:
                raise ValueError(f"Unsupported output format: {report_template.output_format}")
                
        except Exception as e:
            logger.error(f"Failed to generate report: {e}")
            raise
    
    def _collect_report_data(self, template_type, filters):
        """
        Collect data for report generation.
        """
        from analytics.models import TimetableAnalytics, SatisfactionMetric, PreferenceTrend, ConflictAnalysis
        
        data = {}
        
        if template_type == 'efficiency_report':
            # Collect timetable efficiency data
            analytics_qs = TimetableAnalytics.objects.all()
            
            if filters.get('date_range'):
                start_date = filters['date_range']['start']
                end_date = filters['date_range']['end']
                analytics_qs = analytics_qs.filter(
                    analysis_date__range=[start_date, end_date]
                )
            
            if filters.get('department'):
                analytics_qs = analytics_qs.filter(department=filters['department'])
            
            data['efficiency_metrics'] = list(analytics_qs.values())
            
        elif template_type == 'satisfaction_report':
            # Collect satisfaction data
            satisfaction_qs = SatisfactionMetric.objects.all()
            
            if filters.get('entity_type'):
                satisfaction_qs = satisfaction_qs.filter(entity_type=filters['entity_type'])
            
            if filters.get('date_range'):
                start_date = filters['date_range']['start']
                end_date = filters['date_range']['end']
                satisfaction_qs = satisfaction_qs.filter(
                    measurement_period_end__range=[start_date, end_date]
                )
            
            data['satisfaction_metrics'] = list(satisfaction_qs.values())
            
        elif template_type == 'preference_trends_report':
            # Collect preference trend data
            trends_qs = PreferenceTrend.objects.all()
            
            if filters.get('preference_type'):
                trends_qs = trends_qs.filter(preference_type=filters['preference_type'])
            
            if filters.get('user_role'):
                trends_qs = trends_qs.filter(user_role=filters['user_role'])
            
            data['preference_trends'] = list(trends_qs.values())
            
        elif template_type == 'conflict_analysis_report':
            # Collect conflict analysis data
            conflicts_qs = ConflictAnalysis.objects.all()
            
            if filters.get('timetable_id'):
                conflicts_qs = conflicts_qs.filter(timetable_id=filters['timetable_id'])
            
            data['conflict_analyses'] = list(conflicts_qs.values())
        
        return data
    
    def _generate_pdf_report(self, template, data, parameters):
        """
        Generate PDF report.
        """
        try:
            # Create filename
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{template.name}_{timestamp}.pdf"
            file_path = os.path.join(self.output_dir, filename)
            
            # Create PDF document
            doc = SimpleDocTemplate(file_path, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []
            
            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                alignment=1  # Center alignment
            )
            story.append(Paragraph(template.display_name, title_style))
            story.append(Spacer(1, 20))
            
            # Generate content based on template type
            if template.template_type == 'efficiency_report':
                story.extend(self._generate_efficiency_pdf_content(data, styles))
            elif template.template_type == 'satisfaction_report':
                story.extend(self._generate_satisfaction_pdf_content(data, styles))
            elif template.template_type == 'preference_trends_report':
                story.extend(self._generate_trends_pdf_content(data, styles))
            elif template.template_type == 'conflict_analysis_report':
                story.extend(self._generate_conflicts_pdf_content(data, styles))
            
            # Add generation timestamp
            story.append(Spacer(1, 30))
            story.append(Paragraph(
                f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
                styles['Normal']
            ))
            
            # Build PDF
            doc.build(story)
            
            # Get file size
            file_size = os.path.getsize(file_path)
            
            return {
                'file_path': file_path,
                'file_size': file_size,
                'records_processed': len(data.get(list(data.keys())[0], [])) if data else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to generate PDF report: {e}")
            raise
    
    def _generate_efficiency_pdf_content(self, data, styles):
        """
        Generate efficiency report PDF content.
        """
        story = []
        
        efficiency_data = data.get('efficiency_metrics', [])
        if not efficiency_data:
            story.append(Paragraph("No efficiency data available.", styles['Normal']))
            return story
        
        # Convert to DataFrame for analysis
        df = pd.DataFrame(efficiency_data)
        
        # Summary statistics
        story.append(Paragraph("Efficiency Summary", styles['Heading2']))
        
        if 'overall_efficiency_score' in df.columns:
            avg_efficiency = df['overall_efficiency_score'].mean()
            story.append(Paragraph(f"Average Efficiency Score: {avg_efficiency:.2f}%", styles['Normal']))
        
        if 'conflict_rate' in df.columns:
            avg_conflicts = df['conflict_rate'].mean()
            story.append(Paragraph(f"Average Conflict Rate: {avg_conflicts:.2f}%", styles['Normal']))
        
        story.append(Spacer(1, 20))
        
        # Create efficiency chart
        chart_path = self._create_efficiency_chart(df)
        if chart_path:
            story.append(Paragraph("Efficiency Trends", styles['Heading2']))
            story.append(Image(chart_path, width=6*inch, height=4*inch))
            story.append(Spacer(1, 20))
        
        return story
    
    def _create_efficiency_chart(self, df):
        """
        Create efficiency trend chart.
        """
        try:
            if df.empty or 'overall_efficiency_score' not in df.columns:
                return None
            
            plt.figure(figsize=(10, 6))
            
            # Convert analysis_date to datetime if it's a string
            if 'analysis_date' in df.columns:
                df['analysis_date'] = pd.to_datetime(df['analysis_date'])
                df = df.sort_values('analysis_date')
                
                plt.plot(df['analysis_date'], df['overall_efficiency_score'], marker='o')
                plt.title('Timetable Efficiency Over Time')
                plt.xlabel('Date')
                plt.ylabel('Efficiency Score (%)')
                plt.xticks(rotation=45)
            else:
                # If no date column, create a simple bar chart
                plt.bar(range(len(df)), df['overall_efficiency_score'])
                plt.title('Timetable Efficiency Scores')
                plt.xlabel('Timetable')
                plt.ylabel('Efficiency Score (%)')
            
            plt.tight_layout()
            
            # Save chart
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            chart_path = os.path.join(self.output_dir, f'efficiency_chart_{timestamp}.png')
            plt.savefig(chart_path, dpi=settings.CHART_DPI, bbox_inches='tight')
            plt.close()
            
            return chart_path
            
        except Exception as e:
            logger.error(f"Failed to create efficiency chart: {e}")
            plt.close()
            return None
    
    def _generate_excel_report(self, template, data, parameters):
        """
        Generate Excel report.
        """
        try:
            # Create filename
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{template.name}_{timestamp}.xlsx"
            file_path = os.path.join(self.output_dir, filename)
            
            # Create Excel writer
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                
                # Write data to different sheets based on template type
                if template.template_type == 'efficiency_report':
                    efficiency_data = data.get('efficiency_metrics', [])
                    if efficiency_data:
                        df = pd.DataFrame(efficiency_data)
                        df.to_excel(writer, sheet_name='Efficiency Metrics', index=False)
                
                elif template.template_type == 'satisfaction_report':
                    satisfaction_data = data.get('satisfaction_metrics', [])
                    if satisfaction_data:
                        df = pd.DataFrame(satisfaction_data)
                        df.to_excel(writer, sheet_name='Satisfaction Metrics', index=False)
                
                elif template.template_type == 'preference_trends_report':
                    trends_data = data.get('preference_trends', [])
                    if trends_data:
                        df = pd.DataFrame(trends_data)
                        df.to_excel(writer, sheet_name='Preference Trends', index=False)
                
                elif template.template_type == 'conflict_analysis_report':
                    conflicts_data = data.get('conflict_analyses', [])
                    if conflicts_data:
                        df = pd.DataFrame(conflicts_data)
                        df.to_excel(writer, sheet_name='Conflict Analysis', index=False)
                
                # Add summary sheet
                summary_data = {
                    'Report': [template.display_name],
                    'Generated': [timezone.now().strftime('%Y-%m-%d %H:%M:%S')],
                    'Records': [len(data.get(list(data.keys())[0], [])) if data else 0]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
            # Get file size
            file_size = os.path.getsize(file_path)
            
            return {
                'file_path': file_path,
                'file_size': file_size,
                'records_processed': len(data.get(list(data.keys())[0], [])) if data else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to generate Excel report: {e}")
            raise
    
    def _generate_csv_report(self, template, data, parameters):
        """
        Generate CSV report.
        """
        try:
            # Create filename
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{template.name}_{timestamp}.csv"
            file_path = os.path.join(self.output_dir, filename)
            
            # Get the main data based on template type
            if template.template_type == 'efficiency_report':
                main_data = data.get('efficiency_metrics', [])
            elif template.template_type == 'satisfaction_report':
                main_data = data.get('satisfaction_metrics', [])
            elif template.template_type == 'preference_trends_report':
                main_data = data.get('preference_trends', [])
            elif template.template_type == 'conflict_analysis_report':
                main_data = data.get('conflict_analyses', [])
            else:
                main_data = []
            
            if main_data:
                df = pd.DataFrame(main_data)
                df.to_csv(file_path, index=False)
            else:
                # Create empty CSV with headers
                pd.DataFrame().to_csv(file_path, index=False)
            
            # Get file size
            file_size = os.path.getsize(file_path)
            
            return {
                'file_path': file_path,
                'file_size': file_size,
                'records_processed': len(main_data)
            }
            
        except Exception as e:
            logger.error(f"Failed to generate CSV report: {e}")
            raise
    
    def _generate_json_report(self, template, data, parameters):
        """
        Generate JSON report.
        """
        try:
            # Create filename
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{template.name}_{timestamp}.json"
            file_path = os.path.join(self.output_dir, filename)
            
            # Prepare report data
            report_data = {
                'report_info': {
                    'name': template.display_name,
                    'type': template.template_type,
                    'generated_at': timezone.now().isoformat(),
                    'parameters': parameters
                },
                'data': data
            }
            
            # Write JSON file
            with open(file_path, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
            
            # Get file size
            file_size = os.path.getsize(file_path)
            
            return {
                'file_path': file_path,
                'file_size': file_size,
                'records_processed': len(data.get(list(data.keys())[0], [])) if data else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to generate JSON report: {e}")
            raise
    
    def _generate_html_report(self, template, data, parameters):
        """
        Generate HTML report.
        """
        try:
            # Create filename
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{template.name}_{timestamp}.html"
            file_path = os.path.join(self.output_dir, filename)
            
            # Prepare template context
            context = {
                'report_name': template.display_name,
                'generated_at': timezone.now(),
                'data': data,
                'parameters': parameters
            }
            
            # Render template
            if template.template_content:
                django_template = Template(template.template_content)
                html_content = django_template.render(Context(context))
            else:
                # Use default template
                html_content = self._generate_default_html_content(context)
            
            # Write HTML file
            with open(file_path, 'w') as f:
                f.write(html_content)
            
            # Get file size
            file_size = os.path.getsize(file_path)
            
            return {
                'file_path': file_path,
                'file_size': file_size,
                'records_processed': len(data.get(list(data.keys())[0], [])) if data else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to generate HTML report: {e}")
            raise
    
    def _generate_default_html_content(self, context):
        """
        Generate default HTML content for reports.
        """
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{context['report_name']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                h1 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .summary {{ background-color: #f9f9f9; padding: 20px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <h1>{context['report_name']}</h1>
            <div class="summary">
                <p><strong>Generated:</strong> {context['generated_at'].strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <h2>Data Summary</h2>
            <p>This report contains analytics data from the timetable management system.</p>
            
            <h2>Report Data</h2>
            <pre>{json.dumps(context['data'], indent=2, default=str)}</pre>
        </body>
        </html>
        """
        return html
    
    # Additional helper methods for other report types would go here
    def _generate_satisfaction_pdf_content(self, data, styles):
        """Generate satisfaction report PDF content."""
        # Implementation would be similar to efficiency content
        return [Paragraph("Satisfaction report content would go here", styles['Normal'])]
    
    def _generate_trends_pdf_content(self, data, styles):
        """Generate trends report PDF content."""
        # Implementation would be similar to efficiency content
        return [Paragraph("Trends report content would go here", styles['Normal'])]
    
    def _generate_conflicts_pdf_content(self, data, styles):
        """Generate conflicts report PDF content."""
        # Implementation would be similar to efficiency content
        return [Paragraph("Conflicts report content would go here", styles['Normal'])]
