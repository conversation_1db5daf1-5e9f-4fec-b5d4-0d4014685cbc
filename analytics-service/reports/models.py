"""
Report models for the analytics service.
"""
from django.db import models
from django.utils import timezone
from core.models import BaseModel, User
import os


class ReportTemplate(BaseModel):
    """
    Model for report templates.
    """
    TEMPLATE_TYPES = [
        ('efficiency_report', 'Timetable Efficiency Report'),
        ('satisfaction_report', 'Satisfaction Analysis Report'),
        ('preference_trends_report', 'Preference Trends Report'),
        ('conflict_analysis_report', 'Conflict Analysis Report'),
        ('usage_statistics_report', 'Usage Statistics Report'),
        ('custom_report', 'Custom Report'),
    ]
    
    OUTPUT_FORMATS = [
        ('pdf', 'PDF Document'),
        ('excel', 'Excel Spreadsheet'),
        ('csv', 'CSV File'),
        ('json', 'JSON Data'),
        ('html', 'HTML Report'),
    ]
    
    name = models.CharField(max_length=200, unique=True)
    display_name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    template_type = models.Char<PERSON>ield(max_length=50, choices=TEMPLATE_TYPES)
    output_format = models.CharField(max_length=20, choices=OUTPUT_FORMATS)
    
    # Template configuration
    is_default = models.BooleanField(default=False)
    is_public = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Template content
    template_content = models.TextField(help_text="Template content (HTML, JSON, etc.)")
    css_content = models.TextField(blank=True, help_text="CSS styles for HTML reports")
    
    # Parameters and filters
    default_parameters = models.JSONField(default=dict, blank=True)
    available_filters = models.JSONField(default=list, blank=True)
    
    class Meta:
        db_table = 'reports_template'
        ordering = ['template_type', 'name']

    def __str__(self):
        return f"{self.display_name} ({self.get_output_format_display()})"


class Report(BaseModel):
    """
    Model for generated reports.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('generating', 'Generating'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('expired', 'Expired'),
    ]
    
    # Report identification
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, related_name='reports')
    
    # Generation details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    requested_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='requested_reports')
    generated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='generated_reports')
    
    # Time tracking
    requested_at = models.DateTimeField(default=timezone.now)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    # Parameters and filters
    parameters = models.JSONField(default=dict, help_text="Report generation parameters")
    filters = models.JSONField(default=dict, help_text="Data filters applied")
    
    # Output details
    file_path = models.CharField(max_length=500, blank=True)
    file_size = models.PositiveIntegerField(null=True, blank=True, help_text="File size in bytes")
    download_count = models.PositiveIntegerField(default=0)
    
    # Generation metadata
    generation_time_seconds = models.FloatField(null=True, blank=True)
    records_processed = models.PositiveIntegerField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    
    # Sharing and access
    is_public = models.BooleanField(default=False)
    shared_with = models.ManyToManyField(User, blank=True, related_name='shared_reports')
    
    class Meta:
        db_table = 'reports_report'
        ordering = ['-requested_at']
        indexes = [
            models.Index(fields=['status', 'requested_at']),
            models.Index(fields=['template', 'status']),
            models.Index(fields=['requested_by']),
        ]

    def __str__(self):
        return f"{self.name} - {self.get_status_display()}"

    @property
    def filename(self):
        """Generate filename for the report."""
        if self.file_path:
            return os.path.basename(self.file_path)
        
        # Generate filename based on template and timestamp
        timestamp = self.requested_at.strftime('%Y%m%d_%H%M%S')
        extension = self.template.output_format
        return f"{self.template.name}_{timestamp}.{extension}"

    @property
    def is_expired(self):
        """Check if report has expired."""
        return self.expires_at and timezone.now() > self.expires_at

    def mark_as_downloaded(self):
        """Increment download count."""
        self.download_count += 1
        self.save(update_fields=['download_count'])

    def start_generation(self, user=None):
        """Mark report generation as started."""
        self.status = 'generating'
        self.started_at = timezone.now()
        if user:
            self.generated_by = user
        self.save(update_fields=['status', 'started_at', 'generated_by'])

    def complete_generation(self, file_path, file_size=None, records_processed=None):
        """Mark report generation as completed."""
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.file_path = file_path
        self.file_size = file_size
        self.records_processed = records_processed
        
        # Calculate generation time
        if self.started_at:
            self.generation_time_seconds = (self.completed_at - self.started_at).total_seconds()
        
        # Set expiry date (30 days from completion)
        from datetime import timedelta
        self.expires_at = self.completed_at + timedelta(days=30)
        
        self.save(update_fields=[
            'status', 'completed_at', 'file_path', 'file_size', 
            'records_processed', 'generation_time_seconds', 'expires_at'
        ])

    def fail_generation(self, error_message):
        """Mark report generation as failed."""
        self.status = 'failed'
        self.completed_at = timezone.now()
        self.error_message = error_message
        
        # Calculate generation time if started
        if self.started_at:
            self.generation_time_seconds = (self.completed_at - self.started_at).total_seconds()
        
        self.save(update_fields=['status', 'completed_at', 'error_message', 'generation_time_seconds'])


class ReportSchedule(BaseModel):
    """
    Model for scheduled report generation.
    """
    FREQUENCY_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('yearly', 'Yearly'),
    ]
    
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE)
    
    # Scheduling
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES)
    is_enabled = models.BooleanField(default=True)
    
    # Recipients
    recipients = models.ManyToManyField(User, related_name='scheduled_reports')
    
    # Parameters
    parameters = models.JSONField(default=dict)
    filters = models.JSONField(default=dict)
    
    # Execution tracking
    last_run_at = models.DateTimeField(null=True, blank=True)
    next_run_at = models.DateTimeField(null=True, blank=True)
    run_count = models.PositiveIntegerField(default=0)
    
    # Settings
    auto_email = models.BooleanField(default=True, help_text="Automatically email report to recipients")
    retention_days = models.PositiveIntegerField(default=30, help_text="Days to keep generated reports")
    
    class Meta:
        db_table = 'reports_schedule'
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_frequency_display()})"

    def update_next_run(self):
        """Update next run time based on frequency."""
        from datetime import timedelta
        
        if not self.last_run_at:
            self.next_run_at = timezone.now()
        else:
            if self.frequency == 'daily':
                self.next_run_at = self.last_run_at + timedelta(days=1)
            elif self.frequency == 'weekly':
                self.next_run_at = self.last_run_at + timedelta(weeks=1)
            elif self.frequency == 'monthly':
                self.next_run_at = self.last_run_at + timedelta(days=30)
            elif self.frequency == 'quarterly':
                self.next_run_at = self.last_run_at + timedelta(days=90)
            elif self.frequency == 'yearly':
                self.next_run_at = self.last_run_at + timedelta(days=365)
        
        self.save(update_fields=['next_run_at'])

    def mark_as_run(self):
        """Mark schedule as executed."""
        self.last_run_at = timezone.now()
        self.run_count += 1
        self.update_next_run()
        self.save(update_fields=['last_run_at', 'run_count'])
