"""
Celery tasks for report generation.
"""
from celery import shared_task
from django.utils import timezone
from django.conf import settings
from datetime import timed<PERSON><PERSON>
from .models import Report, ReportSchedule
from .services import ReportGeneratorService
import os
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def generate_report_task(self, report_id):
    """
    Generate a report asynchronously.
    """
    try:
        report = Report.objects.get(id=report_id)
        
        # Mark report as generating
        report.start_generation()
        
        # Generate the report
        generator = ReportGeneratorService()
        result = generator.generate_report(
            report.template,
            report.parameters,
            report.filters
        )
        
        # Mark report as completed
        report.complete_generation(
            file_path=result['file_path'],
            file_size=result['file_size'],
            records_processed=result['records_processed']
        )
        
        logger.info(f"Successfully generated report {report_id}")
        
        return {
            'status': 'success',
            'report_id': report_id,
            'file_path': result['file_path'],
            'file_size': result['file_size'],
            'records_processed': result['records_processed']
        }
        
    except Report.DoesNotExist:
        logger.error(f"Report {report_id} not found")
        return {'status': 'failed', 'error': 'Report not found'}
        
    except Exception as e:
        logger.error(f"Failed to generate report {report_id}: {e}")
        
        # Mark report as failed
        try:
            report = Report.objects.get(id=report_id)
            report.fail_generation(str(e))
        except Report.DoesNotExist:
            pass
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'status': 'failed', 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def process_scheduled_reports(self, schedule_id=None):
    """
    Process scheduled reports that are due.
    """
    try:
        now = timezone.now()
        
        if schedule_id:
            # Process specific schedule
            schedules = ReportSchedule.objects.filter(id=schedule_id, is_enabled=True)
        else:
            # Process all due schedules
            schedules = ReportSchedule.objects.filter(
                is_enabled=True,
                next_run_at__lte=now
            )
        
        results = []
        
        for schedule in schedules:
            try:
                # Create reports for all recipients
                for recipient in schedule.recipients.all():
                    report = Report.objects.create(
                        name=f"{schedule.name} - {now.strftime('%Y-%m-%d %H:%M')}",
                        description=f"Scheduled report: {schedule.description}",
                        template=schedule.template,
                        requested_by=recipient,
                        parameters=schedule.parameters,
                        filters=schedule.filters
                    )
                    
                    # Queue report generation
                    generate_report_task.delay(str(report.id))
                    
                    results.append({
                        'schedule_id': str(schedule.id),
                        'report_id': str(report.id),
                        'recipient': recipient.email
                    })
                
                # Mark schedule as run
                schedule.mark_as_run()
                
                logger.info(f"Processed scheduled report {schedule.id} for {schedule.recipients.count()} recipients")
                
            except Exception as e:
                logger.error(f"Failed to process schedule {schedule.id}: {e}")
                continue
        
        return {
            'status': 'success',
            'processed_schedules': len(schedules),
            'generated_reports': len(results),
            'results': results
        }
        
    except Exception as e:
        logger.error(f"Failed to process scheduled reports: {e}")
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'status': 'failed', 'error': str(e)}


@shared_task
def generate_daily_reports():
    """
    Generate daily scheduled reports.
    """
    try:
        # Process all daily schedules
        daily_schedules = ReportSchedule.objects.filter(
            frequency='daily',
            is_enabled=True
        )
        
        results = []
        
        for schedule in daily_schedules:
            task = process_scheduled_reports.delay(str(schedule.id))
            results.append({
                'schedule_id': str(schedule.id),
                'task_id': task.id
            })
        
        logger.info(f"Queued {len(results)} daily report schedules")
        
        return {
            'status': 'success',
            'queued_schedules': len(results),
            'results': results
        }
        
    except Exception as e:
        logger.error(f"Failed to generate daily reports: {e}")
        return {'status': 'failed', 'error': str(e)}


@shared_task
def cleanup_old_reports():
    """
    Clean up old report files and expired reports.
    """
    try:
        now = timezone.now()
        
        # Find expired reports
        expired_reports = Report.objects.filter(
            expires_at__lt=now,
            status='completed'
        )
        
        deleted_files = 0
        deleted_reports = 0
        
        for report in expired_reports:
            try:
                # Delete file if it exists
                if report.file_path and os.path.exists(report.file_path):
                    os.remove(report.file_path)
                    deleted_files += 1
                
                # Mark report as expired or delete it
                report.status = 'expired'
                report.file_path = ''
                report.save()
                deleted_reports += 1
                
            except Exception as e:
                logger.error(f"Failed to cleanup report {report.id}: {e}")
                continue
        
        # Clean up old failed reports (older than 30 days)
        old_failed_reports = Report.objects.filter(
            status='failed',
            created_at__lt=now - timedelta(days=30)
        )
        
        old_failed_count = old_failed_reports.count()
        old_failed_reports.delete()
        
        logger.info(f"Cleaned up {deleted_files} files, {deleted_reports} expired reports, {old_failed_count} old failed reports")
        
        return {
            'status': 'success',
            'deleted_files': deleted_files,
            'expired_reports': deleted_reports,
            'deleted_old_failed': old_failed_count
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup old reports: {e}")
        return {'status': 'failed', 'error': str(e)}


@shared_task
def send_report_notifications():
    """
    Send notifications for completed reports.
    """
    try:
        # Find recently completed reports that need notifications
        recent_time = timezone.now() - timedelta(hours=1)
        
        completed_reports = Report.objects.filter(
            status='completed',
            completed_at__gte=recent_time,
            # Add a field to track if notification was sent
            # notification_sent=False
        )
        
        notifications_sent = 0
        
        for report in completed_reports:
            try:
                # Send notification to report requester
                # This would integrate with the notification service
                
                # For now, just log
                logger.info(f"Would send notification for completed report {report.id} to {report.requested_by.email}")
                notifications_sent += 1
                
                # Mark notification as sent
                # report.notification_sent = True
                # report.save()
                
            except Exception as e:
                logger.error(f"Failed to send notification for report {report.id}: {e}")
                continue
        
        return {
            'status': 'success',
            'notifications_sent': notifications_sent
        }
        
    except Exception as e:
        logger.error(f"Failed to send report notifications: {e}")
        return {'status': 'failed', 'error': str(e)}


@shared_task
def generate_system_reports():
    """
    Generate system-wide analytics reports.
    """
    try:
        from analytics.models import TimetableAnalytics
        
        # Check if we have recent analytics data
        recent_analytics = TimetableAnalytics.objects.filter(
            analysis_date__gte=timezone.now() - timedelta(days=1)
        ).count()
        
        if recent_analytics == 0:
            logger.warning("No recent analytics data available for system reports")
            return {'status': 'skipped', 'reason': 'No recent data'}
        
        # Generate efficiency report
        from .models import ReportTemplate
        
        try:
            efficiency_template = ReportTemplate.objects.get(
                template_type='efficiency_report',
                is_default=True
            )
            
            # Create system efficiency report
            system_report = Report.objects.create(
                name=f"System Efficiency Report - {timezone.now().strftime('%Y-%m-%d')}",
                description="Automated daily system efficiency report",
                template=efficiency_template,
                requested_by=None,  # System generated
                parameters={'automated': True},
                filters={
                    'date_range': {
                        'start': (timezone.now() - timedelta(days=7)).isoformat(),
                        'end': timezone.now().isoformat()
                    }
                }
            )
            
            # Queue generation
            generate_report_task.delay(str(system_report.id))
            
            logger.info(f"Generated system efficiency report {system_report.id}")
            
            return {
                'status': 'success',
                'report_id': str(system_report.id)
            }
            
        except ReportTemplate.DoesNotExist:
            logger.warning("Default efficiency report template not found")
            return {'status': 'skipped', 'reason': 'Template not found'}
        
    except Exception as e:
        logger.error(f"Failed to generate system reports: {e}")
        return {'status': 'failed', 'error': str(e)}
