"""
Celery configuration for analytics_service project.
"""
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'analytics_service.settings')

app = Celery('analytics_service')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery beat schedule for periodic tasks
app.conf.beat_schedule = {
    'update-analytics-data': {
        'task': 'analytics.tasks.update_analytics_data',
        'schedule': 3600.0,  # Run every hour
    },
    'generate-daily-reports': {
        'task': 'reports.tasks.generate_daily_reports',
        'schedule': 86400.0,  # Run daily
    },
    'cleanup-old-analytics-data': {
        'task': 'analytics.tasks.cleanup_old_analytics_data',
        'schedule': 86400.0,  # Run daily
    },
    'update-dashboard-cache': {
        'task': 'dashboard.tasks.update_dashboard_cache',
        'schedule': 300.0,  # Run every 5 minutes
    },
    'cleanup-old-reports': {
        'task': 'reports.tasks.cleanup_old_reports',
        'schedule': 86400.0,  # Run daily
    },
    'calculate-satisfaction-metrics': {
        'task': 'analytics.tasks.calculate_satisfaction_metrics',
        'schedule': 3600.0,  # Run every hour
    },
    'analyze-preference-trends': {
        'task': 'analytics.tasks.analyze_preference_trends',
        'schedule': 21600.0,  # Run every 6 hours
    },
}

app.conf.timezone = settings.TIME_ZONE

# Task routing
app.conf.task_routes = {
    'analytics.tasks.*': {'queue': 'analytics'},
    'reports.tasks.*': {'queue': 'reports'},
    'dashboard.tasks.*': {'queue': 'dashboard'},
}

# Task configuration
app.conf.task_soft_time_limit = 300  # 5 minutes
app.conf.task_time_limit = 600  # 10 minutes
app.conf.worker_prefetch_multiplier = 1

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
