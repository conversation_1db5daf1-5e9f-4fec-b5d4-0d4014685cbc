#!/bin/bash

# Verification Script for DAGITO Docker Images
# This script verifies that all images can be pulled from DockerHub

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

DOCKER_USERNAME="hust001"

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# List of images to verify
images=(
    "dagito-auth-service:latest"
    "dagito-user-service:latest"
    "dagito-preference-service:latest"
    "dagito-timetable-service:latest"
    "dagito-notification-service:latest"
    "dagito-analytics-service:latest"
    "dagito-frontend:latest"
)

print_status "Starting verification of DAGITO Docker images on DockerHub"
print_status "Docker Hub Username: $DOCKER_USERNAME"
echo

failed_images=()
successful_images=()

for image in "${images[@]}"; do
    full_image="$DOCKER_USERNAME/$image"
    print_status "Verifying $full_image..."
    
    # Try to pull the image
    if docker pull "$full_image" >/dev/null 2>&1; then
        print_success "✓ $full_image is available and pullable"
        successful_images+=("$image")
    else
        print_error "✗ Failed to pull $full_image"
        failed_images+=("$image")
    fi
    echo
done

# Summary
echo "=================================="
print_status "Verification Summary"
echo "=================================="

if [ ${#successful_images[@]} -gt 0 ]; then
    print_success "Successfully verified images (${#successful_images[@]}):"
    for image in "${successful_images[@]}"; do
        echo "  ✓ $DOCKER_USERNAME/$image"
    done
    echo
fi

if [ ${#failed_images[@]} -gt 0 ]; then
    print_error "Failed to verify images (${#failed_images[@]}):"
    for image in "${failed_images[@]}"; do
        echo "  ✗ $DOCKER_USERNAME/$image"
    done
    echo
    exit 1
else
    print_success "All DAGITO Docker images are available on DockerHub!"
    echo
    print_status "You can now deploy using:"
    echo "  - docker-compose up (update image names in docker-compose.yml)"
    echo "  - kubectl apply -f k8s/ (update image names in K8s manifests)"
    echo "  - Individual docker run commands"
fi

print_status "Images are ready for production deployment!"
