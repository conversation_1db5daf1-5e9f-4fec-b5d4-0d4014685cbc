# DAGITO Production Environment Configuration

# Database Configuration
MONGODB_URI=***************************************************************************
REDIS_URL=redis://redis:6379/0

# Security Configuration
DEBUG=False
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
INTERNAL_SERVICE_TOKEN=your-internal-service-token-change-this

# Service URLs (Internal Communication)
AUTH_SERVICE_URL=http://auth-service:8001
USER_SERVICE_URL=http://user-service:8002
PREFERENCE_SERVICE_URL=http://preference-service:8003
TIMETABLE_SERVICE_URL=http://timetable-service:8004
NOTIFICATION_SERVICE_URL=http://notification-service:8005
ANALYTICS_SERVICE_URL=http://analytics-service:8006

# External URLs (for frontend)
REACT_APP_API_BASE_URL=http://localhost:8000/api
REACT_APP_WS_BASE_URL=ws://localhost:8000/ws
REACT_APP_ENV=production

# Email Configuration (Update with your SMTP settings)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=DAGITO System <<EMAIL>>

# Preference Service Configuration
PREFERENCE_VALIDATION_ENABLED=True
PREFERENCE_CONFLICT_DETECTION=True
PREFERENCE_AUTO_SAVE_INTERVAL=30
DEFAULT_TIME_SLOT_DURATION=60
WORKING_HOURS_START=08:00
WORKING_HOURS_END=18:00
WORKING_DAYS=monday,tuesday,wednesday,thursday,friday

# Timetable Service Configuration
GENETIC_ALGORITHM_POPULATION_SIZE=100
GENETIC_ALGORITHM_GENERATIONS=50
GENETIC_ALGORITHM_MUTATION_RATE=0.1
GENETIC_ALGORITHM_CROSSOVER_RATE=0.8
TIMETABLE_OPTIMIZATION_TIMEOUT=300
CONFLICT_DETECTION_ENABLED=True
AUTO_CONFLICT_RESOLUTION=True

# Notification Service Configuration
WEBSOCKET_ENABLED=True
SMS_ENABLED=False

# Analytics Service Configuration
ANALYTICS_DATA_RETENTION_DAYS=365
REPORT_GENERATION_ENABLED=True
REAL_TIME_ANALYTICS=True

# Allowed Hosts (Update with your domain)
ALLOWED_HOSTS=localhost,127.0.0.1,api-gateway,your-domain.com
